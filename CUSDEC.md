# Prompt: Python Microservice for UN/EDIFACT CUSDEC Generation

## Overview
Create a complete Python microservice application that exposes an HTTP endpoint which, when called with appropriate data, processes the information and generates a valid UN/EDIFACT CUSDEC (Customs Declaration) message. The application should follow best practices for microservices, including proper error handling, logging, configuration management, and testing.

## Required Functionality
1. HTTP endpoint that accepts JSON payload containing customs declaration data
2. Data validation and transformation logic
3. UN/EDIFACT CUSDEC message generation according to D96B or later standard
4. Error handling with appropriate HTTP response codes
5. Logging system for operations and errors
6. Configurable settings (via environment variables or config files)
7. Documentation for API usage and deployment

## Implementation Steps

### Step 1: Project Structure and Setup
Create a project with the following structure:
- Directory layout (main application, tests, configuration, documentation)
- Dependencies (requirements.txt)
- Configuration management
- Environment setup instructions

### Step 2: Core UN/EDIFACT Components
Implement the core components needed for EDIFACT message creation:
- Segment classes
- Data element handling
- Message structure builders
- CUSDEC-specific segments and data elements

### Step 3: Data Models and Validation
Create models that:
- Define the expected input data structure
- Implement validation rules
- Handle data type conversions
- Map input data to EDIFACT elements

### Step 4: API Implementation
Build the HTTP service with:
- API endpoint definition
- Request processing
- Response formatting
- HTTP status code handling
- Rate limiting (optional)

### Step 5: CUSDEC Message Generation
Implement the logic to:
- Transform validated data into EDIFACT format
- Build the complete CUSDEC message
- Format according to EDIFACT standards
- Validate the generated message

### Step 6: Testing Framework
Create comprehensive tests for:
- Unit tests for EDIFACT component generation
- Integration tests for the API
- Example input/output test cases
- Test coverage reporting

### Step 7: Documentation
Provide:
- API documentation
- Input data format requirements
- Example requests and responses
- Deployment instructions
- Configuration options

### Step 8: Deployment Configuration
Include:
- Dockerfile
- Docker Compose file (optional)
- Environment configuration examples
- Health check endpoint

## Important Considerations
- Ensure compliance with UN/EDIFACT D96B (or specified version) standards for CUSDEC
- Include proper error handling for all edge cases
- Implement logging for troubleshooting
- Support configuration via environment variables
- Document any limitations or assumptions

## When Issues Arise
When needing to fix an issue, regenerate the entire codebase with the fix implemented, maintaining all context from previous versions. Each regeneration should:
1. Incorporate all previous functionality
2. Fix the specific issue
3. Update any related components that might be affected
4. Update tests to cover the fixed functionality
5. Document the changes and reason for the fix

## Example Request/Response Format
Include examples of:
- Valid JSON input format
- Expected EDIFACT CUSDEC output
- Error response structure

## Troubleshooting Section
Include a section on common issues and their solutions, focusing on:
- Data validation errors
- EDIFACT formatting issues
- API usage problems
- Deployment challenges