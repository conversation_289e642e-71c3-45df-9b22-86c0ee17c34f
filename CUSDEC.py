"""
CUSDEC Microservice - UN/EDIFACT Customs Declaration Message Generator

This microservice provides an HTTP endpoint to generate valid UN/EDIFACT CUSDEC
messages from JSON input data according to D96B standards.

This is the main entry point that imports from the refactored modules.
"""

# Import the main application
from main import app

# Re-export for backward compatibility
from edifact.core import EDIFACTElement, EDIFACTSegment, EDIFACTMessage
from cusdec.builder import CUSDECBuilder
from models import CUSDECRequest, CUSDECResponse

__all__ = [
    "app", 
    "EDIFACTElement", 
    "EDIFACTSegment", 
    "EDIFACTMessage", 
    "CUSDECBuilder",
    "CUSDECRequest",
    "CUSDECResponse"
]



