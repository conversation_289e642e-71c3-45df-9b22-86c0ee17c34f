# CUSDEC Microservice for SARS

A microservice for generating UN/EDIFACT CUSDEC (Customs Declaration) messages according to South African Revenue Service (SARS) requirements and D96B standards.

## Overview

This microservice provides an HTTP API endpoint that accepts JSON data containing customs declaration information and returns a properly formatted UN/EDIFACT CUSDEC message compliant with SARS requirements. It follows best practices for microservices including proper error handling, logging, configuration management, and testing.

The implementation is based on the SARS Customs EDI User Manual (G016) and the SC-CF-55 Goods Declaration External Policy.

## Features

- Complete CRUD operations for CUSDEC records
- HTTP endpoint for generating CUSDEC messages
- Data validation and transformation
- UN/EDIFACT CUSDEC message generation (D96B standard)
- SARS-specific fields and validation
- SARS-compliant EDIFACT segment generation
- Database storage of CUSDEC records
- Comprehensive error handling
- Configurable via environment variables
- Logging for operations and errors
- Docker support for easy deployment

## Requirements

- Python 3.9+
- FastAPI
- Uvicorn
- Pydantic
- Python-dotenv

## Installation

### Using Docker (Recommended)

1. Clone the repository
2. Navigate to the project directory
3. Build and run using Docker Compose:

```bash
docker-compose up -d
```

### Manual Installation

1. Clone the repository
2. Navigate to the project directory
3. Create a virtual environment:

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

4. Install dependencies:

```bash
pip install -r requirements.txt
```

5. Configure environment variables (see Configuration section)
6. Run the application:

```bash
python run_cusdec.py
```

## Configuration

The application can be configured using environment variables or a `.env` file:

| Variable | Description | Default |
|----------|-------------|---------|
| PORT | HTTP port | 8000 |
| HOST | HTTP host | 0.0.0.0 |
| LOG_FILE | Log file path | cusdec_service.log |
| EDIFACT_VERSION | EDIFACT version | D96B |
| UNA_SEGMENT | UNA segment value | UNA:+.? ' |
| SYNTAX_IDENTIFIER | Syntax identifier | UNOC |
| SYNTAX_VERSION | Syntax version | 3 |
| SENDER_ID | Sender ID | SENDER123 |
| RECIPIENT_ID | Recipient ID | SARSZA |
| DEBUG_MODE | Enable debug mode | False |
| ALLOWED_ORIGINS | CORS allowed origins | * |

## API Usage

### CRUD Operations

#### Create CUSDEC Record

**Endpoint:** `POST /api/v1/cusdec`

**Request Format:**

```json
{
  "documentNameCode": "952",
  "documentNumber": "TESTDOC123",
  "messageFunction": "9",
  "documentDate": "230501",
  "declarationDate": "230501",
  "placeOfDeparture": "DEHAM",
  "placeOfDestination": "USLAX",
  "references": [
    {
      "type": "ABT",
      "number": "REF123456"
    }
  ],
  "parties": {
    "exporter": {
      "partyIdQualifier": "ZZZ",
      "partyId": "EXPORTER123",
      "name": "Test Exporter",
      "street": "123 Export St",
      "city": "Hamburg",
      "postcode": "20457",
      "countryCode": "DE",
      "# SARS-specific fields": "",
      "tinNumber": "TIN123456",
      "branchCode": "001",
      "customsCode": "CC789012"
    },
    "importer": {
      "partyIdQualifier": "ZZZ",
      "partyId": "IMPORTER456",
      "name": "Test Importer",
      "street": "456 Import Ave",
      "city": "Los Angeles",
      "postcode": "90001",
      "countryCode": "US"
    },
    "declarant": {
      "partyIdQualifier": "ZZZ",
      "partyId": "DECLARANT789",
      "name": "Test Declarant",
      "street": "789 Customs Rd",
      "city": "Hamburg",
      "postcode": "20457",
      "countryCode": "DE"
    }
  },
  "goodsItems": [
    {
      "description": "Laptop Computers",
      "packageType": "BX",
      "packageCount": 10,
      "grossWeight": 100.5,
      "netWeight": 95.0,
      "commodityCode": "8471.30.0000",
      "originCountry": "DE",
      "itemValue": 5000.00,
      "containerNumbers": ["CONT123456"],
      "# SARS-specific fields": "",
      "customsValue": 5500.00,
      "dutyValue": 550.00,
      "vatValue": 825.00,
      "tariffCode": "8471.30.01",
      "previousDocumentReference": "PREV123456",
      "preferentialTreatment": "A"
    }
  ],
  "controlResult": "A",
  "authenticationType": "1",
  "authenticationPlace": "DEHAM",

  "# SARS-specific fields": "",
  "declarationType": "SAD500",
  "customsOfficeCode": "JHB",
  "declarantReference": "REF789012",
  "transportMode": "1",
  "previousCustomsRegime": "40",
  "valueAddedTaxNumber": "4123456789"
}
```

**Response Format:**

```json
{
  "message": "UNA:+.? 'UNB+UNOC:3+SENDER123+CUSTOMS456+230501:1200+ABC123456789'UNH+DEF123456789+CUSDEC:D96B:UN:CD9611'BGM+952+TESTDOC123+9'...",
  "messageReference": "DEF123456789"
}
```

#### Read CUSDEC Records

**Endpoint:** `GET /api/v1/cusdec`

**Query Parameters:**
- `skip` (optional): Number of records to skip (default: 0)
- `limit` (optional): Maximum number of records to return (default: 100)

**Response Format:**

```json
{
  "total": 2,
  "records": [
    {
      "id": 1,
      "documentNumber": "TESTDOC123",
      "documentNameCode": "952",
      "messageFunction": "9",
      "documentDate": "230501",
      "declarationDate": "230501",
      "placeOfDeparture": "DEHAM",
      "placeOfDestination": "USLAX",
      "references": [...],
      "parties": {...},
      "goodsItems": [...],
      "controlResult": "A",
      "authenticationType": "1",
      "authenticationPlace": "DEHAM",
      "edifactMessage": "UNA:+.? '...",
      "messageReference": "DEF123456789",
      "createdAt": "2023-05-01T12:00:00",
      "updatedAt": "2023-05-01T12:00:00"
    },
    {...}
  ]
}
```

#### Read Single CUSDEC Record

**Endpoint:** `GET /api/v1/cusdec/{record_id}`

**Response Format:**

```json
{
  "id": 1,
  "documentNumber": "TESTDOC123",
  "documentNameCode": "952",
  "messageFunction": "9",
  "documentDate": "230501",
  "declarationDate": "230501",
  "placeOfDeparture": "DEHAM",
  "placeOfDestination": "USLAX",
  "references": [...],
  "parties": {...},
  "goodsItems": [...],
  "controlResult": "A",
  "authenticationType": "1",
  "authenticationPlace": "DEHAM",
  "edifactMessage": "UNA:+.? '...",
  "messageReference": "DEF123456789",
  "createdAt": "2023-05-01T12:00:00",
  "updatedAt": "2023-05-01T12:00:00"
}
```

#### Update CUSDEC Record

**Endpoint:** `PUT /api/v1/cusdec/{record_id}`

**Request Format:**

```json
{
  "documentNumber": "UPDATED123",
  "placeOfDeparture": "NLRTM",
  "placeOfDestination": "USLAX"
}
```

**Response Format:**

Same as the Read Single CUSDEC Record response.

#### Delete CUSDEC Record

**Endpoint:** `DELETE /api/v1/cusdec/{record_id}`

**Response:** HTTP 204 No Content

### Health Check

**Endpoint:** `GET /health`

**Response:**

```json
{
  "status": "healthy",
  "version": "1.0.0"
}
```

## Error Handling

The API returns appropriate HTTP status codes and error messages:

- `400 Bad Request`: Invalid input data
- `422 Unprocessable Entity`: Validation errors
- `500 Internal Server Error`: Unexpected errors

Error responses include detailed information about the issue.

## Testing

Run the tests using pytest:

```bash
pytest
```

## SARS-Specific Requirements

This implementation includes support for SARS-specific requirements as outlined in the Customs G016 EDI User Manual and SC-CF-55 Goods Declaration External Policy. The implementation follows the UN/EDIFACT D96B standard with SARS-specific customizations.

### SARS-Specific Fields

#### Party Information
- **tinNumber**: SARS Trader Identification Number (10-digit number)
- **branchCode**: SARS Branch Code (3-digit number)
- **customsCode**: SARS Customs Client Code (5-10 characters)

#### Declaration Information
- **declarationType**: SARS Declaration Type Code (e.g., SAD500, SAD501, SAD502, etc.)
- **customsOfficeCode**: SARS Customs Office Code (3-character code, e.g., JHB for Johannesburg)
- **declarantReference**: Declarant's Reference Number (used for tracking purposes)
- **transportMode**: Mode of Transport Code (1=Maritime, 2=Rail, 3=Road, 4=Air, etc.)
- **previousCustomsRegime**: Previous Customs Regime Code (used for goods previously under another customs procedure)
- **valueAddedTaxNumber**: VAT Registration Number (10-digit number starting with 4)

#### Goods Item Information
- **customsValue**: Customs value in ZAR (used for duty calculation)
- **dutyValue**: Duty value in ZAR (amount of customs duty payable)
- **vatValue**: VAT value in ZAR (amount of VAT payable)
- **tariffCode**: SARS Tariff Code (at least 8 characters, more specific than HS code)
- **previousDocumentReference**: Reference to previous document (for linked declarations)
- **preferentialTreatment**: Preferential treatment code (A-P, indicating special tariff treatments)

### SARS-Specific Segments

The following EDIFACT segments are included to support SARS requirements:

- **DOC**: Document details for declaration type and previous documents
  - Used to specify the SAD document type (SAD500, SAD501, etc.)
  - Used for referencing previous documents in goods items

- **CTA**: Contact information for customs office
  - Specifies the customs office handling the declaration

- **LOC**: Location information
  - Specifies customs clearance office location

- **DTM**: Date/time information
  - Includes clearance date in YYMMDD format

- **TDT**: Transport details
  - Specifies mode of transport (sea, air, road, rail)

- **PAC**: Package information
  - Includes previous customs regime information

- **RFF**: Reference information
  - Used for VAT number and declarant reference

- **MOA**: Monetary amount
  - Used for customs value in goods items

- **TAX**: Tax information
  - Used for duty and VAT amounts
  - Includes tax type codes (A00 for customs duty, VAT for value-added tax)

- **PIA**: Product identification
  - Used when SARS tariff code differs from HS commodity code

- **ALC**: Allowance information
  - Used for preferential treatment codes

### Validation Rules

The implementation includes validation for SARS-specific fields:

- **TIN Number**: Must be a 10-digit number
- **Branch Code**: Must be a 3-digit number
- **Customs Code**: Must be between 5 and 10 characters
- **Declaration Type**: Must be one of the valid SAD document types
- **Customs Office Code**: Must be a 3-character code
- **Transport Mode**: Must be one of the valid transport mode codes
- **VAT Number**: Must be a 10-digit number starting with 4
- **Date Format**: Must be in YYMMDD format with valid month and day values

### Configuration

The following environment variables can be set to configure SARS-specific behavior:

- **RECIPIENT_ID**: Set to "SARSZA" for SARS customs authority
- **SARS_OFFICE_CODE**: Default customs office code (e.g., "JHB")
- **SARS_CLIENT_CODE**: Default customs client code
- **SARS_BRANCH_CODE**: Default branch code

### SARS EDI Communication

This implementation follows the SARS EDI User Manual specifications for message structure and communication. The CUSDEC message is formatted according to the D96B standard with SARS-specific requirements as outlined in the SARS EDI User Manual.

## Troubleshooting

### Common Issues

#### Invalid Date Format

Dates must be in YYMMDD format (e.g., 230501 for May 1, 2023).

#### Missing Required Fields

Ensure all required fields are provided in the request. The following fields are mandatory:
- documentNumber
- documentDate
- declarationDate
- placeOfDeparture
- placeOfDestination
- parties (must include at least exporter and importer)
- goodsItems (must include at least one item)

#### EDIFACT Formatting Issues

If the generated EDIFACT message is not being accepted by the receiving system:
- Check the configuration settings for separators and terminators
- Verify that the EDIFACT version is compatible with the receiving system
- Ensure all required segments are included in the request

#### API Connection Issues

If you cannot connect to the API:
- Verify the service is running
- Check the host and port configuration
- Ensure network connectivity and firewall settings

## License

[MIT License](LICENSE)

## Sample Files

### Sample Request JSON

A sample request file is included in the repository as `sample_request.json`. You can use this file to test the API.

### Testing Script

You can use the included `test_api.py` script to test the API from the command line:

```bash
python test_api.py http://localhost:8000/api/cusdec
```
