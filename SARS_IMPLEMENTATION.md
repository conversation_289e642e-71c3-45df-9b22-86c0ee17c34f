# SARS CUSDEC Implementation

This document provides a detailed overview of the SARS-specific implementation of the CUSDEC service.

## Overview

The CUSDEC service has been implemented according to South African Revenue Service (SARS) guidelines as outlined in the following documents:

1. **Customs-G016-EDI-User-Manual-June-2016-External-Guide.pdf**
   - Contains detailed specifications for CUSDEC message structure
   - Defines SARS-specific requirements for EDI communications
   - Provides data mapping guides and branching diagrams

2. **SC-CF-55-Goods-Declaration-External-Policy.pdf**
   - Outlines the policy for goods declarations
   - Defines requirements for supporting documents
   - Specifies procedures for imports, exports, and transfers

3. **ISO 9735-3.pdf**
   - Provides the EDIFACT syntax rules for interactive EDI
   - Defines the structure of EDIFACT messages

## Implementation Details

### Configuration Settings

The following configuration settings have been added for SARS-specific requirements:

```python
# SARS CUSDEC Configuration
EDIFACT_VERSION = os.getenv("EDIFACT_VERSION", "D96B")
UNA_SEGMENT = os.getenv("UNA_SEGMENT", "UNA:+.? '")
SYNTAX_IDENTIFIER = os.getenv("SYNTAX_IDENTIFIER", "UNOC")
SYNTAX_VERSION = os.getenv("SYNTAX_VERSION", "3")
SENDER_ID = os.getenv("SENDER_ID", "SENDER123")
RECIPIENT_ID = os.getenv("RECIPIENT_ID", "SARSZA")
# SARS-specific configuration
SARS_OFFICE_CODE = os.getenv("SARS_OFFICE_CODE", "JHB")
SARS_CLIENT_CODE = os.getenv("SARS_CLIENT_CODE", "")
SARS_BRANCH_CODE = os.getenv("SARS_BRANCH_CODE", "")
```

### Data Models

The following SARS-specific fields have been added to the data models:

#### Party Model

```python
class Party(BaseModel):
    # Existing fields...
    
    # SARS-specific fields
    tinNumber: Optional[str] = Field(None, description="SARS Trader Identification Number")
    branchCode: Optional[str] = Field(None, description="SARS Branch Code")
    customsCode: Optional[str] = Field(None, description="SARS Customs Client Code")
    
    # Validators for SARS-specific fields...
```

#### GoodsItem Model

```python
class GoodsItem(BaseModel):
    # Existing fields...
    
    # SARS-specific fields
    customsValue: Optional[float] = Field(None, description="Customs value in ZAR")
    dutyValue: Optional[float] = Field(None, description="Duty value in ZAR")
    vatValue: Optional[float] = Field(None, description="VAT value in ZAR")
    tariffCode: Optional[str] = Field(None, description="SARS Tariff Code")
    previousDocumentReference: Optional[str] = Field(None, description="Reference to previous document")
    preferentialTreatment: Optional[str] = Field(None, description="Preferential treatment code")
    
    # Validators for SARS-specific fields...
```

#### CUSDECRequest Model

```python
class CUSDECRequest(BaseModel):
    # Existing fields...
    
    # SARS-specific fields
    declarationType: Optional[str] = Field(None, description="SARS Declaration Type Code")
    customsOfficeCode: Optional[str] = Field(None, description="SARS Customs Office Code")
    declarantReference: Optional[str] = Field(None, description="Declarant's Reference Number")
    transportMode: Optional[str] = Field(None, description="Mode of Transport Code")
    previousCustomsRegime: Optional[str] = Field(None, description="Previous Customs Regime Code")
    valueAddedTaxNumber: Optional[str] = Field(None, description="VAT Registration Number")
    
    # Validators for SARS-specific fields...
```

### Validation Rules

Validation rules have been implemented for SARS-specific fields:

- **TIN Number**: Must be a 10-digit number
- **Branch Code**: Must be a 3-digit number
- **Customs Code**: Must be between 5 and 10 characters
- **Declaration Type**: Must be one of the valid SAD document types (SAD500, SAD501, etc.)
- **Customs Office Code**: Must be a 3-character code
- **Transport Mode**: Must be one of the valid transport mode codes (1-9)
- **VAT Number**: Must be a 10-digit number starting with 4
- **Date Format**: Must be in YYMMDD format with valid month and day values

### EDIFACT Message Generation

The following SARS-specific segments have been added to the EDIFACT message generation:

#### _add_sars_specific_segments Method

```python
def _add_sars_specific_segments(self):
    """Add SARS-specific segments to the CUSDEC message."""
    # Declaration Type (DOC segment)
    # Customs Office Code (CTA segment)
    # Transport Mode (TDT segment)
    # Previous Customs Regime (PAC segment)
    # VAT Number (RFF segment)
    # Declarant Reference (RFF segment)
    # SARS-specific LOC segment for customs office
    # SARS-specific DTM segment for clearance date
```

#### Enhanced _add_goods_items Method

```python
# SARS-specific goods item fields
# TAX - Duty and Tax information
# Customs value information (MOA segment)
# Duty information (TAX segment)
# VAT information (TAX segment)
# Previous document reference (DOC segment)
# Preferential treatment (ALC segment)
# SARS-specific tariff code (PIA segment)
```

### Database Model

The database model has been updated to include SARS-specific fields:

```python
class CUSDECRecord(Base):
    # Existing fields...
    
    # SARS-specific fields
    declaration_type = Column(String, nullable=True)
    customs_office_code = Column(String, nullable=True)
    declarant_reference = Column(String, nullable=True)
    transport_mode = Column(String, nullable=True)
    previous_customs_regime = Column(String, nullable=True)
    value_added_tax_number = Column(String, nullable=True)
```

### Testing

Tests have been implemented to verify the SARS-specific implementation:

- Test validation of SARS TIN number
- Test validation of SARS VAT number
- Test validation of SARS declaration type
- Test generation of SARS-specific segments in EDIFACT message

## Example Usage

A sample SARS CUSDEC request is provided in `examples/sars_cusdec_request.json`. This request can be used to generate a SARS-compliant CUSDEC message using the `examples/generate_sars_cusdec.py` script.

## Conclusion

The CUSDEC service has been successfully implemented according to SARS guidelines. The implementation includes all the required SARS-specific fields, validation rules, and EDIFACT segments as outlined in the SARS documentation.
