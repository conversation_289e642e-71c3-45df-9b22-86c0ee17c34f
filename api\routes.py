"""
API routes for CUSDEC service.
"""

import logging
from typing import List
from fastapi import APIRouter, HTTPException, Depends, status, Query, Path
from sqlalchemy.orm import Session

from database import get_db, CUSDECRecord
from models import (
    CUSDECRequest, CUSDECResponse, CUSDECRecordCreate, 
    CUSDECRecordUpdate, CUSDECRecordResponse
)
from cusdec.builder import CUSDECBuilder
from validators.d96b_validator import SARSCUSDECValidator

logger = logging.getLogger("cusdec_service")

router = APIRouter()


@router.post("/api/v1/cusdec", response_model=CUSDECResponse, status_code=status.HTTP_201_CREATED)
async def generate_cusdec(request_data: CUSDECRequest, db: Session = Depends(get_db)):
    """Generate a UN/EDIFACT CUSDEC message from the provided data."""
    logger.info(f"Received CUSDEC request with document number: {request_data.documentNumber}")

    try:
        # Convert request to dict
        request_dict = request_data.model_dump()

        # Validate SARS CUSDEC format if applicable
        validator = SARSCUSDECValidator()
        validation_report = validator.get_validation_report(request_dict)
        if not validation_report["valid"]:
            logger.warning(f"SARS CUSDEC validation failed: {validation_report['errors']}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "SARS CUSDEC validation failed",
                    "errors": validation_report["errors"],
                    "format": validation_report.get("format", "Unknown"),
                    "specification": validation_report.get("specification", "SARS CUSDEC_30_2")
                }
            )
        logger.info(f"SARS CUSDEC validation passed ({validation_report.get('format', 'Unknown')} format)")

        # Build the CUSDEC message
        builder = CUSDECBuilder(request_dict)
        edifact_message = builder.build()

        # Create the response
        response = CUSDECResponse(
            message=edifact_message,
            messageReference=builder.message.message_ref
        )

        # Save to database
        data = request_data.model_dump()
        record = CUSDECRecord(
            document_number=data["documentNumber"],
            document_name_code=data["documentNameCode"],
            message_function=data["messageFunction"],
            document_date=data["documentDate"],
            declaration_date=data["declarationDate"],
            place_of_departure=data["placeOfDeparture"],
            place_of_destination=data["placeOfDestination"],
            control_result=data.get("controlResult"),
            authentication_type=data.get("authenticationType"),
            authentication_place=data.get("authenticationPlace"),
            declaration_type=data.get("declarationType"),
            customs_office_code=data.get("customsOfficeCode"),
            declarant_reference=data.get("declarantReference"),
            transport_mode=data.get("transportMode"),
            exchange_rate=data.get("exchangeRate"),
            exchange_rate_date=data.get("exchangeRateDate"),
            total_invoice_amount=data.get("totalInvoiceAmount"),
            total_customs_value=data.get("totalCustomsValue"),
            total_duty_amount=data.get("totalDutyAmount"),
            total_vat_amount=data.get("totalVatAmount"),
            edifact_message=edifact_message,
            message_reference=builder.message.message_ref
        )
        db.add(record)
        db.commit()
        db.refresh(record)

        logger.info(f"Successfully generated CUSDEC message with reference: {response.messageReference}")
        return response

    except ValueError as e:
        logger.error(f"Error generating CUSDEC message: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

    except Exception as e:
        logger.error(f"Unexpected error generating CUSDEC message: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate CUSDEC message due to an internal error"
        )


@router.get("/api/v1/cusdec", response_model=List[CUSDECRecordResponse])
async def list_cusdec_records(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    db: Session = Depends(get_db)
):
    """List all CUSDEC records."""
    records = db.query(CUSDECRecord).offset(skip).limit(limit).all()
    return records


@router.get("/api/v1/cusdec/{record_id}", response_model=CUSDECRecordResponse)
async def get_cusdec_record(record_id: int = Path(..., description="Record ID"), db: Session = Depends(get_db)):
    """Get a specific CUSDEC record."""
    record = db.query(CUSDECRecord).filter(CUSDECRecord.id == record_id).first()
    if not record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="CUSDEC record not found"
        )
    return record


@router.put("/api/v1/cusdec/{record_id}", response_model=CUSDECRecordResponse)
async def update_cusdec_record(
    record_id: int = Path(..., description="Record ID"),
    update_data: CUSDECRecordUpdate = ...,
    db: Session = Depends(get_db)
):
    """Update a CUSDEC record."""
    record = db.query(CUSDECRecord).filter(CUSDECRecord.id == record_id).first()
    if not record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="CUSDEC record not found"
        )

    # Update fields
    update_dict = update_data.model_dump(exclude_unset=True)
    for field, value in update_dict.items():
        setattr(record, field, value)

    db.commit()
    db.refresh(record)
    return record


@router.delete("/api/v1/cusdec/{record_id}")
async def delete_cusdec_record(record_id: int = Path(..., description="Record ID"), db: Session = Depends(get_db)):
    """Delete a CUSDEC record."""
    record = db.query(CUSDECRecord).filter(CUSDECRecord.id == record_id).first()
    if not record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="CUSDEC record not found"
        )

    db.delete(record)
    db.commit()
    return {"message": "CUSDEC record deleted successfully"}


@router.post("/api/v1/cusdec/validate-sars", status_code=status.HTTP_200_OK)
async def validate_sars_cusdec(request_data: dict):
    """Validate a CUSDEC message against SARS CUSDEC_30_2 specification."""
    logger.info("Received SARS CUSDEC validation request")

    try:
        validator = SARSCUSDECValidator()
        validation_report = validator.get_validation_report(request_data)

        logger.info(f"SARS CUSDEC validation completed: {validation_report['valid']}")
        return {
            "valid": validation_report["valid"],
            "message": validation_report["message"],
            "error_count": validation_report["error_count"],
            "errors": validation_report["errors"],
            "format": validation_report.get("format", "Unknown"),
            "specification": validation_report.get("specification", "SARS CUSDEC_30_2")
        }

    except Exception as e:
        logger.error(f"Error during SARS CUSDEC validation: {str(e)}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


