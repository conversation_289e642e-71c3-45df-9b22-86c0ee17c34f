"""
Configuration module for CUSDEC service.
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class Config:
    """Application configuration loaded from environment variables."""

    # SARS CUSDEC Configuration
    EDIFACT_VERSION = os.getenv("EDIFACT_VERSION", "30")
    UNA_SEGMENT = os.getenv("UNA_SEGMENT", "UNA:+.? '")
    SYNTAX_IDENTIFIER = os.getenv("SYNTAX_IDENTIFIER", "UNOB")
    SYNTAX_VERSION = os.getenv("SYNTAX_VERSION", "4")
    SENDER_ID = os.getenv("SENDER_ID", "")
    SENDER_AUTH_CODE = os.getenv("SENDER_AUTH_CODE", "")
    SENDER_SP_CODE = os.getenv("SENDER_SP_CODE", "")
    SENDER_COMMS_CODE = os.getenv("SENDER_COMMS_CODE", "AS2")
    RECIPIENT_ID = os.getenv("RECIPIENT_ID", "SARSDEC")
    RECIPIENT_TEST = os.getenv("RECIPIENT_TEST", "False").lower() == "true"
    
    # SARS-specific configuration
    SARS_OFFICE_CODE = os.getenv("SARS_OFFICE_CODE", "JHB")
    SARS_CLIENT_CODE = os.getenv("SARS_CLIENT_CODE", "")
    SARS_BRANCH_CODE = os.getenv("SARS_BRANCH_CODE", "")
    
    # EDIFACT formatting
    DECIMAL_SEPARATOR = os.getenv("DECIMAL_SEPARATOR", ".")
    SEGMENT_TERMINATOR = os.getenv("SEGMENT_TERMINATOR", "'")
    DATA_ELEMENT_SEPARATOR = os.getenv("DATA_ELEMENT_SEPARATOR", "+")
    COMPONENT_DATA_SEPARATOR = os.getenv("COMPONENT_DATA_SEPARATOR", ":")
    RELEASE_CHARACTER = os.getenv("RELEASE_CHARACTER", "?")
    DEBUG_MODE = os.getenv("DEBUG_MODE", "False").lower() == "true"

