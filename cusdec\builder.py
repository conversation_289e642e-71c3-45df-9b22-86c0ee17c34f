"""
CUSDEC message builder for SARS-specific requirements.
"""

import logging
from typing import Dict, Any
from edifact.core import EDIFACTMessage, EDIFACTSegment, EDIFACTElement
from config import Config

logger = logging.getLogger("cusdec_service")


class CUSDECBuilder:
    """Builder class for CUSDEC messages with D96B support."""

    def __init__(self, data: Dict[str, Any]):
        self.data = data
        self.message = EDIFACTMessage("CUSDEC")
        self.is_sars_format = self._check_sars_format()

    def _check_sars_format(self) -> bool:
        """Check if the input data is in SARS CUSDEC format."""
        return "messageHeader" in self.data and "beginningOfMessage" in self.data

    def build(self) -> str:
        """Build the complete CUSDEC message according to SARS specifications."""
        if self.is_sars_format:
            return self._build_sars_message()
        else:
            return self._build_legacy_message()

    def _build_sars_message(self) -> str:
        """Build SARS CUSDEC_30_2 compliant message."""
        try:
            logger.info("Building SARS CUSDEC_30_2 message")

            # Add BGM from beginningOfMessage
            self._add_sars_bgm_segment()

            # Add DTM segments from dateTimePeriod
            self._add_sars_dtm_segments()

            # Add LOC segments from locations
            self._add_sars_loc_segments()

            # Add RFF segments from references
            self._add_sars_rff_segments()

            # Add NAD segments from nameAndAddress
            self._add_sars_nad_segments()

            # Add goods items with SARS structure
            self._add_sars_goods_items()

            # Add control totals
            self._add_sars_control_totals()

            # Add authentication result
            self._add_sars_authentication()

            logger.info("SARS CUSDEC_30_2 message built successfully")
            return self.message.format()

        except Exception as e:
            logger.error(f"Error building SARS CUSDEC message: {str(e)}")
            raise ValueError(f"Failed to build SARS CUSDEC message: {str(e)}")

    def _build_legacy_message(self) -> str:
        """Build legacy CUSDEC message for backward compatibility."""
        try:
            logger.info("Adding BGM segment")
            self._add_bgm_segment()

            logger.info("Adding CST segment (Customs Status of Goods)")
            self._add_cst_segment()

            logger.info("Adding DTM segments")
            self._add_dtm_segments()

            logger.info("Adding LOC segments")
            self._add_loc_segments()

            logger.info("Adding reference segments")
            self._add_reference_segments()

            logger.info("Adding SARS-specific segments")
            self._add_sars_specific_segments()

            logger.info("Adding NAD segments")
            self._add_nad_segments()

            logger.info("Adding UNS segment (Section Control)")
            self._add_uns_segment("D")

            logger.info("Adding goods items")
            self._add_goods_items()

            logger.info("Adding UNS segment (Section Control)")
            self._add_uns_segment("S")

            logger.info("Adding summary segments")
            self._add_summary_segments()

            logger.info("Adding control result segment")
            self._add_cntres_segment()

            logger.info("Adding authentication segment")
            self._add_autcst_segment()

            logger.info("Formatting message")
            return self.message.format()
        except Exception as e:
            logger.error(f"Error building CUSDEC message: {str(e)}")
            logger.error(f"Exception type: {type(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise ValueError(f"Failed to build CUSDEC message: {str(e)}")

    def _add_bgm_segment(self):
        """Add Beginning of Message segment."""
        doc_name_code = self.data.get("documentNameCode", "952")
        doc_number = self.data.get("documentNumber", "")
        message_function = self.data.get("messageFunction", "9")

        self.message.add_segment(EDIFACTSegment("BGM", [
            [EDIFACTElement(doc_name_code)],
            EDIFACTElement(doc_number),
            EDIFACTElement(message_function)
        ]))

    def _add_cst_segment(self):
        """Add Customs Status of Goods segment (CST) as required by SARS."""
        customs_procedure = self.data.get("customsProcedure", "")
        previous_procedure = self.data.get("previousProcedure", "")

        self.message.add_segment(EDIFACTSegment("CST", [
            [EDIFACTElement(customs_procedure), EDIFACTElement(previous_procedure)]
        ]))

    def _add_dtm_segments(self):
        """Add Date/Time segments."""
        # Document date
        doc_date = self.data.get("documentDate")
        if doc_date:
            self.message.add_segment(EDIFACTSegment("DTM", [
                [EDIFACTElement("137"), EDIFACTElement(doc_date), EDIFACTElement("102")]
            ]))

        # Declaration date
        decl_date = self.data.get("declarationDate")
        if decl_date:
            self.message.add_segment(EDIFACTSegment("DTM", [
                [EDIFACTElement("132"), EDIFACTElement(decl_date), EDIFACTElement("102")]
            ]))

    def _add_loc_segments(self):
        """Add Location segments."""
        # Place of departure
        departure = self.data.get("placeOfDeparture")
        if departure:
            self.message.add_segment(EDIFACTSegment("LOC", [
                EDIFACTElement("5"),
                [EDIFACTElement(departure), EDIFACTElement("162")]
            ]))

        # Place of destination
        destination = self.data.get("placeOfDestination")
        if destination:
            self.message.add_segment(EDIFACTSegment("LOC", [
                EDIFACTElement("7"),
                [EDIFACTElement(destination), EDIFACTElement("162")]
            ]))

    def _add_reference_segments(self):
        """Add Reference segments."""
        references = self.data.get("references", [])
        for ref in references:
            self.message.add_segment(EDIFACTSegment("RFF", [
                [EDIFACTElement(ref.get("qualifier", "")), EDIFACTElement(ref.get("number", ""))]
            ]))

    def _add_sars_specific_segments(self):
        """Add SARS-specific segments to the CUSDEC message."""
        # Declaration Type (DOC segment)
        declaration_type = self.data.get("declarationType")
        if declaration_type is not None:
            self.message.add_segment(EDIFACTSegment("DOC", [
                EDIFACTElement("ZZZ"),
                [EDIFACTElement(declaration_type), EDIFACTElement("271")]
            ]))

        # Customs Office Code (CTA segment)
        customs_office_code = self.data.get("customsOfficeCode") or Config.SARS_OFFICE_CODE
        if customs_office_code is not None:
            self.message.add_segment(EDIFACTSegment("CTA", [
                EDIFACTElement("CR"),
                [EDIFACTElement(""), EDIFACTElement(customs_office_code)]
            ]))

        # Add SARS-specific LOC segment for customs office
        if customs_office_code is not None:
            self.message.add_segment(EDIFACTSegment("LOC", [
                EDIFACTElement("42"),
                [EDIFACTElement(customs_office_code), EDIFACTElement("162")]
            ]))

        # Add SARS-specific DTM segment for clearance date
        declaration_date = self.data.get("declarationDate")
        if declaration_date is not None:
            self.message.add_segment(EDIFACTSegment("DTM", [
                [EDIFACTElement("132"), EDIFACTElement(declaration_date), EDIFACTElement("102")]
            ]))

    def _add_nad_segments(self):
        """Add Name and Address segments."""
        parties = self.data.get("parties", {})

        # Exporter
        if "exporter" in parties:
            exporter = parties["exporter"]
            self.message.add_segment(EDIFACTSegment("NAD", [
                EDIFACTElement("EX"),
                EDIFACTElement(exporter.get("partyIdQualifier", "")),
                [EDIFACTElement(exporter.get("partyId", ""))],
                EDIFACTElement(exporter.get("name", "")),
                EDIFACTElement(exporter.get("street", "")),
                EDIFACTElement(exporter.get("city", "")),
                EDIFACTElement(""),
                EDIFACTElement(exporter.get("postcode", "")),
                EDIFACTElement(exporter.get("countryCode", ""))
            ]))

        # Importer
        if "importer" in parties:
            importer = parties["importer"]
            self.message.add_segment(EDIFACTSegment("NAD", [
                EDIFACTElement("IM"),
                EDIFACTElement(importer.get("partyIdQualifier", "")),
                [EDIFACTElement(importer.get("partyId", ""))],
                EDIFACTElement(importer.get("name", "")),
                EDIFACTElement(importer.get("street", "")),
                EDIFACTElement(importer.get("city", "")),
                EDIFACTElement(""),
                EDIFACTElement(importer.get("postcode", "")),
                EDIFACTElement(importer.get("countryCode", ""))
            ]))

        # Declarant
        if "declarant" in parties:
            declarant = parties["declarant"]
            self.message.add_segment(EDIFACTSegment("NAD", [
                EDIFACTElement("DT"),
                EDIFACTElement(declarant.get("partyIdQualifier", "")),
                [EDIFACTElement(declarant.get("partyId", ""))],
                EDIFACTElement(declarant.get("name", "")),
                EDIFACTElement(declarant.get("street", "")),
                EDIFACTElement(declarant.get("city", "")),
                EDIFACTElement(""),
                EDIFACTElement(declarant.get("postcode", "")),
                EDIFACTElement(declarant.get("countryCode", ""))
            ]))

    def _add_uns_segment(self, section_code: str):
        """Add Section Control segment (UNS) as required by SARS."""
        self.message.add_segment(EDIFACTSegment("UNS", [
            EDIFACTElement(section_code)
        ]))

    def _add_goods_items(self):
        """Add goods item segments."""
        items = self.data.get("goodsItems", [])
        logger.info(f"Goods items: {items}")

        for item in items:
            # LIN segment for line item
            self.message.add_segment(EDIFACTSegment("LIN", [
                EDIFACTElement(item.get("itemNumber", "")),
                EDIFACTElement(""),
                [EDIFACTElement(item.get("commodityCode", "")), EDIFACTElement("HS")]
            ]))

            # IMD segment for item description
            self.message.add_segment(EDIFACTSegment("IMD", [
                EDIFACTElement("F"),
                EDIFACTElement(""),
                [EDIFACTElement("", ""), EDIFACTElement(""), EDIFACTElement(""), EDIFACTElement(item.get("goodsDescription", ""))]
            ]))

            # QTY segment for quantity
            self.message.add_segment(EDIFACTSegment("QTY", [
                [EDIFACTElement("12"), EDIFACTElement(item.get("quantity", "")), EDIFACTElement(item.get("quantityUnit", ""))]
            ]))

            # MOA segment for monetary amounts
            if item.get("itemValue"):
                self.message.add_segment(EDIFACTSegment("MOA", [
                    [EDIFACTElement("203"), EDIFACTElement(item.get("itemValue", ""))]
                ]))

    def _add_summary_segments(self):
        """Add summary segments as required by SARS."""
        # Add total values
        total_invoice_value = sum(float(item.get("itemValue", 0)) for item in self.data.get("goodsItems", []))
        total_customs_value = sum(float(item.get("customsValue", 0)) for item in self.data.get("goodsItems", []))
        total_duty_value = sum(float(item.get("dutyValue", 0)) for item in self.data.get("goodsItems", []))
        total_vat_value = sum(float(item.get("vatValue", 0)) for item in self.data.get("goodsItems", []))

        # Total invoice amount
        if total_invoice_value > 0:
            self.message.add_segment(EDIFACTSegment("MOA", [
                [EDIFACTElement("128"), EDIFACTElement(str(total_invoice_value))]
            ]))

        # Total customs value
        if total_customs_value > 0:
            self.message.add_segment(EDIFACTSegment("MOA", [
                [EDIFACTElement("125"), EDIFACTElement(str(total_customs_value))]
            ]))

    def _add_cntres_segment(self):
        """Add Control Result segment."""
        logger.info(f"Control result: {self.data.get('controlResult')}")
        control_result = self.data.get("controlResult")
        if control_result is not None:
            self.message.add_segment(EDIFACTSegment("CNT", [
                [EDIFACTElement("2"), EDIFACTElement(control_result)]
            ]))

    def _add_autcst_segment(self):
        """Add Authentication segment."""
        auth_type = self.data.get("authenticationType")
        auth_place = self.data.get("authenticationPlace")
        
        if auth_type or auth_place:
            self.message.add_segment(EDIFACTSegment("AUT", [
                EDIFACTElement(auth_type or ""),
                EDIFACTElement(auth_place or "")
            ]))

    # SARS-specific methods
    def _add_sars_bgm_segment(self):
        """Add BGM segment from SARS beginningOfMessage structure."""
        bgm_data = self.data.get("beginningOfMessage", {})
        doc_name_code = bgm_data.get("documentNameCode", "952")
        doc_number = bgm_data.get("documentNumber", "")
        message_function = bgm_data.get("messageFunction", "9")
        response_type = bgm_data.get("responseType", "")

        elements = [
            [EDIFACTElement(doc_name_code)],
            EDIFACTElement(doc_number),
            EDIFACTElement(message_function)
        ]

        if response_type:
            elements.append(EDIFACTElement(response_type))

        self.message.add_segment(EDIFACTSegment("BGM", elements))

    def _add_sars_dtm_segments(self):
        """Add DTM segments from SARS dateTimePeriod structure."""
        for dtm_data in self.data.get("dateTimePeriod", []):
            qualifier = dtm_data.get("qualifier", "")
            date_time = dtm_data.get("dateTime", "")
            format_qualifier = dtm_data.get("formatQualifier", "102")

            self.message.add_segment(EDIFACTSegment("DTM", [
                [EDIFACTElement(qualifier), EDIFACTElement(date_time), EDIFACTElement(format_qualifier)]
            ]))

    def _add_sars_loc_segments(self):
        """Add LOC segments from SARS locations structure."""
        for loc_data in self.data.get("locations", []):
            qualifier = loc_data.get("qualifier", "")
            loc_id_data = loc_data.get("locationIdentification", {})
            location_id = loc_id_data.get("locationId", "")
            code_list_qualifier = loc_id_data.get("codeListQualifier", "")
            code_list_agency = loc_id_data.get("codeListResponsibleAgency", "")

            elements = [EDIFACTElement(qualifier)]

            if location_id:
                loc_elements = [EDIFACTElement(location_id)]
                if code_list_qualifier:
                    loc_elements.append(EDIFACTElement(code_list_qualifier))
                if code_list_agency:
                    loc_elements.append(EDIFACTElement(code_list_agency))
                elements.append(loc_elements)

            self.message.add_segment(EDIFACTSegment("LOC", elements))

    def _add_sars_rff_segments(self):
        """Add RFF segments from SARS references structure."""
        for ref_data in self.data.get("references", []):
            qualifier = ref_data.get("qualifier", "")
            reference_number = ref_data.get("referenceNumber", "")
            line_identifier = ref_data.get("documentLineIdentifier", "")

            elements = [[EDIFACTElement(qualifier), EDIFACTElement(reference_number)]]
            if line_identifier:
                elements.append(EDIFACTElement(line_identifier))

            self.message.add_segment(EDIFACTSegment("RFF", elements))

    def _add_sars_nad_segments(self):
        """Add NAD segments from SARS nameAndAddress structure."""
        for nad_data in self.data.get("nameAndAddress", []):
            party_qualifier = nad_data.get("partyQualifier", "")
            party_id_data = nad_data.get("partyIdentification", {})
            name_address_data = nad_data.get("nameAndAddress", {})

            elements = [EDIFACTElement(party_qualifier)]

            # Add party identification
            if party_id_data:
                party_id = party_id_data.get("partyId", "")
                code_list_qualifier = party_id_data.get("codeListQualifier", "")
                if party_id:
                    party_elements = [EDIFACTElement(party_id)]
                    if code_list_qualifier:
                        party_elements.append(EDIFACTElement(code_list_qualifier))
                    elements.append(party_elements)

            # Add name and address
            if name_address_data:
                name = name_address_data.get("name", "")
                street = name_address_data.get("street", "")
                city = name_address_data.get("city", "")
                postcode = name_address_data.get("postcode", "")
                country_code = name_address_data.get("countryCode", "")

                if name:
                    elements.append([EDIFACTElement(name)])
                if street:
                    elements.append([EDIFACTElement(street)])
                if city:
                    elements.append(EDIFACTElement(city))
                if postcode:
                    elements.append(EDIFACTElement(postcode))
                if country_code:
                    elements.append(EDIFACTElement(country_code))

            self.message.add_segment(EDIFACTSegment("NAD", elements))

    def _add_sars_goods_items(self):
        """Add goods items from SARS goodsItems structure."""
        for item_data in self.data.get("goodsItems", []):
            # Add LIN segment
            line_item = item_data.get("lineItem", {})
            if line_item:
                line_number = line_item.get("lineItemNumber", "")
                item_id_data = line_item.get("itemNumberIdentification", {})

                elements = []
                if line_number:
                    elements.append(EDIFACTElement(line_number))

                if item_id_data:
                    item_number = item_id_data.get("itemNumber", "")
                    item_type = item_id_data.get("itemNumberType", "")
                    if item_number:
                        item_elements = [EDIFACTElement(item_number)]
                        if item_type:
                            item_elements.append(EDIFACTElement(item_type))
                        elements.append(item_elements)

                if elements:
                    self.message.add_segment(EDIFACTSegment("LIN", elements))

            # Add other item-related segments (IMD, MEA, QTY, MOA, etc.)
            self._add_sars_item_details(item_data)

    def _add_sars_item_details(self, item_data):
        """Add detailed segments for a goods item."""
        # Add IMD segment for item description
        item_desc = item_data.get("itemDescription", {})
        if item_desc:
            desc_type = item_desc.get("itemDescriptionType", "F")
            description = item_desc.get("itemDescription", "")

            self.message.add_segment(EDIFACTSegment("IMD", [
                EDIFACTElement(desc_type),
                "",  # Empty element
                [EDIFACTElement(""), EDIFACTElement(""), EDIFACTElement(description)]
            ]))

        # Add MEA segments for measurements
        for mea_data in item_data.get("measurements", []):
            qualifier = mea_data.get("measurementQualifier", "")
            mea_details = mea_data.get("measurementDetails", {})
            value = mea_details.get("measurementValue", "")
            unit = mea_details.get("measurementUnitCode", "")

            self.message.add_segment(EDIFACTSegment("MEA", [
                EDIFACTElement(qualifier),
                "",  # Empty element
                [EDIFACTElement(unit), EDIFACTElement(value)]
            ]))

        # Add QTY segments for quantities
        for qty_data in item_data.get("quantities", []):
            qualifier = qty_data.get("quantityQualifier", "")
            qty_details = qty_data.get("quantityDetails", {})
            quantity = qty_details.get("quantity", "")
            unit = qty_details.get("measurementUnitCode", "")

            self.message.add_segment(EDIFACTSegment("QTY", [
                [EDIFACTElement(qualifier), EDIFACTElement(quantity), EDIFACTElement(unit)]
            ]))

        # Add MOA segments for monetary amounts
        for moa_data in item_data.get("monetaryAmounts", []):
            qualifier = moa_data.get("monetaryAmountQualifier", "")
            moa_details = moa_data.get("monetaryAmountDetails", {})
            currency_qualifier = moa_details.get("currencyQualifier", "")
            amount = moa_details.get("monetaryAmount", "")
            currency = moa_details.get("currencyCode", "")

            self.message.add_segment(EDIFACTSegment("MOA", [
                [EDIFACTElement(qualifier), EDIFACTElement(amount), EDIFACTElement(currency), EDIFACTElement(currency_qualifier)]
            ]))

    def _add_sars_control_totals(self):
        """Add control total segments from SARS controlTotal structure."""
        for control_data in self.data.get("controlTotal", []):
            qualifier = control_data.get("controlQualifier", "")
            value = control_data.get("controlValue", "")

            self.message.add_segment(EDIFACTSegment("CNT", [
                [EDIFACTElement(qualifier), EDIFACTElement(value)]
            ]))

    def _add_sars_authentication(self):
        """Add authentication segment from SARS authenticationResult structure."""
        auth_data = self.data.get("authenticationResult", {})
        if auth_data:
            validation_result = auth_data.get("validationResult", "1")
            validation_key = auth_data.get("validationKeyName", "SARS_CUSDEC_VALIDATION")

            self.message.add_segment(EDIFACTSegment("AUT", [
                EDIFACTElement(validation_result),
                EDIFACTElement(validation_key)
            ]))

