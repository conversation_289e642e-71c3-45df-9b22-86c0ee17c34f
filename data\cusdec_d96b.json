{"openapi": "3.0.1", "info": {"title": "EdiNation API", "version": "2"}, "paths": {}, "components": {"schemas": {"EDIFACT_ID_0051": {"enum": ["AA", "AB", "AC", "AD", "AE", "AF", "AG", "AH", "AI", "CC", "CE", "EC", "ED", "EE", "EN", "ER", "EU", "EW", "EX", "IA", "KE", "LI", "OD", "RI", "RT", "UN"], "type": "string", "format": "EDIFACT_ID_0051"}, "EDIFACT_ID_0113": {"enum": ["AA", "AB", "AC", "AD", "AE", "AF", "AG", "AH", "AI", "AJ", "AK", "AL", "AM", "AN", "AO", "AP", "AQ", "AR", "AS", "SECACK", "SECAUT"], "type": "string", "format": "EDIFACT_ID_0113"}, "S009": {"required": ["ControllingAgencyCoded_04", "MessageReleaseNumber_03", "MessageType_01", "MessageVersionNumber_02"], "type": "object", "properties": {"MessageType_01": {"maxLength": 6, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "0065"}, "MessageVersionNumber_02": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "0052"}, "MessageReleaseNumber_03": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "0054"}, "ControllingAgencyCoded_04": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_0051"}], "x-openedi-element-id": "0051"}, "AssociationAssignedCode_05": {"maxLength": 6, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "0057"}, "CodelistDirectoryVersionNumber_06": {"maxLength": 6, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "0110"}, "MessageTypeSubfunctionIdentification_07": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_0113"}], "x-openedi-element-id": "0113"}}, "x-openedi-composite-id": "S009"}, "S010": {"required": ["SequenceMessageTransferNumber_01"], "type": "object", "properties": {"SequenceMessageTransferNumber_01": {"maxLength": 2, "minLength": 1, "type": "string", "format": "EDIFACT_N", "x-openedi-element-id": "0070"}, "FirstLastSequenceMessageTransferIndication_02": {"maxLength": 1, "minLength": 1, "type": "string", "format": "EDIFACT_A", "x-openedi-element-id": "0073"}}, "x-openedi-composite-id": "S010"}, "S016": {"required": ["MessageSubsetIdentification_01"], "type": "object", "properties": {"MessageSubsetIdentification_01": {"maxLength": 14, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "0115"}, "MessageSubsetVersionNumber_02": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "0116"}, "MessageSubsetReleaseNumber_03": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "0118"}, "ControllingAgencyCoded_04": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "0051"}}, "x-openedi-composite-id": "S016"}, "S017": {"required": ["MessageImplementationGuidelineIdentification_01"], "type": "object", "properties": {"MessageImplementationGuidelineIdentification_01": {"maxLength": 14, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "0121"}, "MessageImplementationGuidelineVersionNumber_02": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "0122"}, "MessageImplementationGuidelineReleaseNumber_03": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "0124"}, "ControllingAgencyCoded_04": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "0051"}}, "x-openedi-composite-id": "S017"}, "S018": {"required": ["ScenarioIdentification_01"], "type": "object", "properties": {"ScenarioIdentification_01": {"maxLength": 14, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "0127"}, "ScenarioVersionNumber_02": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "0128"}, "ScenarioReleaseNumber_03": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "0130"}, "ControllingAgencyCoded_04": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "0051"}}, "x-openedi-composite-id": "S018"}, "UNH": {"required": ["MessageIdentifier_02", "MessageReferenceNumber_01"], "type": "object", "properties": {"MessageReferenceNumber_01": {"maxLength": 14, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "0062"}, "MessageIdentifier_02": {"$ref": "#/components/schemas/S009"}, "CommonAccessReference_03": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "0068"}, "StatusoftheTransfer_04": {"$ref": "#/components/schemas/S010"}, "MessageSubsetIdentification_05": {"$ref": "#/components/schemas/S016"}, "MessageImplementationGuidelineIdentification_06": {"$ref": "#/components/schemas/S017"}, "ScenarioIdentification_07": {"$ref": "#/components/schemas/S018"}}, "x-openedi-segment-id": "UNH"}, "EDIFACT_ID_1001": {"enum": ["1", "10", "100", "101", "105", "11", "110", "12", "120", "13", "130", "14", "140", "15", "150", "16", "17", "18", "19", "190", "2", "20", "201", "202", "203", "204", "205", "206", "207", "208", "209", "21", "210", "211", "212", "215", "22", "220", "221", "222", "223", "224", "225", "226", "227", "228", "229", "23", "230", "231", "232", "233", "24", "240", "241", "242", "245", "25", "26", "27", "270", "271", "28", "29", "3", "30", "31", "310", "311", "315", "32", "320", "325", "326", "327", "328", "33", "330", "335", "34", "340", "341", "343", "345", "35", "350", "351", "36", "37", "370", "38", "380", "381", "382", "383", "384", "385", "386", "387", "388", "389", "39", "390", "393", "394", "395", "396", "4", "40", "409", "41", "412", "42", "425", "426", "427", "428", "429", "43", "430", "431", "435", "44", "447", "448", "45", "450", "451", "452", "454", "455", "456", "457", "458", "460", "465", "466", "467", "468", "469", "47", "48", "481", "485", "49", "490", "491", "492", "493", "5", "520", "530", "550", "575", "580", "59", "6", "60", "61", "610", "62", "621", "622", "623", "624", "63", "630", "631", "632", "633", "635", "64", "640", "65", "650", "655", "66", "67", "68", "69", "7", "70", "700", "701", "702", "703", "704", "705", "706", "707", "708", "709", "71", "710", "711", "712", "713", "714", "715", "716", "72", "720", "722", "723", "724", "73", "730", "74", "740", "741", "743", "744", "745", "746", "75", "750", "76", "760", "761", "763", "764", "765", "766", "77", "770", "775", "78", "780", "781", "782", "783", "784", "785", "786", "787", "788", "789", "79", "790", "791", "792", "793", "794", "795", "796", "797", "798", "799", "8", "80", "81", "810", "811", "812", "82", "820", "821", "822", "823", "824", "825", "83", "830", "833", "84", "840", "841", "85", "850", "851", "852", "853", "855", "856", "86", "860", "861", "862", "863", "864", "865", "87", "870", "88", "89", "890", "895", "896", "9", "90", "901", "91", "910", "911", "913", "914", "915", "916", "917", "92", "925", "926", "927", "929", "93", "930", "931", "932", "933", "934", "935", "936", "937", "938", "94", "940", "941", "95", "950", "951", "952", "953", "954", "955", "96", "960", "961", "962", "963", "964", "965", "966", "97", "98", "99", "990", "991", "995", "996", "998"], "type": "string", "format": "EDIFACT_ID_1001"}, "EDIFACT_ID_1131": {"enum": ["100", "101", "102", "103", "104", "105", "106", "107", "108", "109", "110", "112", "113", "114", "115", "116", "117", "118", "119", "12", "120", "121", "122", "123", "125", "126", "127", "128", "129", "130", "131", "132", "133", "134", "135", "136", "137", "139", "140", "141", "142", "143", "144", "145", "146", "147", "148", "149", "150", "151", "152", "153", "154", "155", "156", "157", "158", "16", "160", "161", "162", "163", "164", "165", "166", "167", "168", "169", "170", "172", "173", "174", "175", "176", "177", "178", "179", "180", "181", "182", "183", "184", "185", "186", "187", "188", "189", "190", "191", "192", "23", "25", "35", "36", "37", "38", "39", "42", "43", "44", "52", "53", "54", "55", "56", "57", "58", "59", "60", "61", "62", "63", "64", "65", "67", "68", "69", "70", "71", "72", "ZZZ"], "type": "string", "format": "EDIFACT_ID_1131"}, "EDIFACT_ID_3055": {"enum": ["1", "10", "100", "101", "102", "103", "104", "105", "106", "107", "108", "109", "11", "110", "111", "112", "113", "114", "115", "116", "117", "118", "119", "12", "120", "121", "122", "123", "124", "125", "126", "128", "129", "13", "130", "131", "132", "133", "134", "135", "136", "137", "138", "139", "14", "140", "141", "142", "143", "144", "145", "146", "147", "148", "149", "15", "150", "151", "152", "153", "154", "155", "156", "157", "158", "159", "16", "160", "161", "162", "163", "164", "165", "166", "167", "168", "169", "17", "170", "171", "172", "173", "174", "175", "176", "177", "178", "179", "18", "181", "182", "183", "184", "19", "2", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "3", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "4", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "5", "50", "51", "52", "53", "54", "55", "56", "57", "58", "59", "6", "60", "61", "62", "63", "64", "65", "66", "67", "68", "69", "7", "70", "71", "72", "73", "74", "75", "76", "77", "78", "79", "8", "80", "81", "82", "83", "86", "87", "88", "89", "9", "90", "91", "92", "93", "94", "95", "96", "97", "98", "99", "ZZZ"], "type": "string", "format": "EDIFACT_ID_3055"}, "C002": {"type": "object", "properties": {"Documentmessagenamecoded_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1001"}], "x-openedi-element-id": "1001"}, "Codelistqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}, "Documentmessagename_04": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "1000"}}, "x-openedi-composite-id": "C002"}, "C106": {"type": "object", "properties": {"Documentmessagenumber_01": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "1004"}, "Version_02": {"maxLength": 9, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "1056"}, "Revisionnumber_03": {"maxLength": 6, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "1060"}}, "x-openedi-composite-id": "C106"}, "EDIFACT_ID_1225": {"enum": ["1", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "2", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "3", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "4", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "5", "50", "51", "52", "53", "54", "6", "7", "8", "9"], "type": "string", "format": "EDIFACT_ID_1225"}, "EDIFACT_ID_4343": {"enum": ["AA", "AB", "AC", "AD", "AF", "AG", "AI", "AP", "CA", "CO", "NA", "RE"], "type": "string", "format": "EDIFACT_ID_4343"}, "BGM": {"type": "object", "properties": {"DOCUMENTMESSAGENAME_01": {"$ref": "#/components/schemas/C002"}, "DOCUMENTMESSAGEIDENTIFICATION_02": {"$ref": "#/components/schemas/C106"}, "Messagefunctioncoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1225"}], "x-openedi-element-id": "1225"}, "Responsetypecoded_04": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_4343"}], "x-openedi-element-id": "4343"}}, "x-openedi-segment-id": "BGM"}, "C246": {"required": ["Customscodeidentification_01"], "type": "object", "properties": {"Customscodeidentification_01": {"maxLength": 18, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "7361"}, "Codelistqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}}, "x-openedi-composite-id": "C246"}, "CST": {"type": "object", "properties": {"Goodsitemnumber_01": {"maxLength": 5, "minLength": 1, "type": "string", "format": "EDIFACT_N", "x-openedi-element-id": "1496"}, "CUSTOMSIDENTITYCODES_02": {"$ref": "#/components/schemas/C246"}, "CUSTOMSIDENTITYCODES_03": {"$ref": "#/components/schemas/C246"}, "CUSTOMSIDENTITYCODES_04": {"$ref": "#/components/schemas/C246"}, "CUSTOMSIDENTITYCODES_05": {"$ref": "#/components/schemas/C246"}, "CUSTOMSIDENTITYCODES_06": {"$ref": "#/components/schemas/C246"}}, "x-openedi-segment-id": "CST"}, "EDIFACT_ID_3227": {"enum": ["1", "10", "100", "101", "102", "103", "104", "105", "106", "107", "108", "109", "11", "110", "111", "113", "114", "115", "116", "118", "119", "12", "120", "121", "122", "123", "124", "125", "126", "127", "128", "129", "13", "130", "131", "132", "133", "134", "135", "136", "137", "138", "139", "14", "140", "141", "142", "143", "144", "145", "146", "147", "148", "149", "15", "151", "152", "153", "154", "155", "156", "157", "158", "159", "16", "160", "161", "162", "163", "164", "165", "166", "167", "168", "169", "17", "170", "18", "19", "2", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "4", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "5", "50", "51", "52", "53", "54", "55", "56", "57", "58", "60", "61", "62", "64", "66", "67", "68", "69", "7", "70", "71", "72", "73", "74", "75", "76", "77", "78", "79", "8", "80", "81", "82", "83", "84", "85", "86", "87", "88", "89", "9", "90", "91", "92", "93", "96", "97", "98", "99", "ZZZ"], "type": "string", "format": "EDIFACT_ID_3227"}, "C517": {"type": "object", "properties": {"Placelocationidentification_01": {"maxLength": 25, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3225"}, "Codelistqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}, "Placelocation_04": {"maxLength": 70, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3224"}}, "x-openedi-composite-id": "C517"}, "C519": {"type": "object", "properties": {"Relatedplacelocationoneidentification_01": {"maxLength": 25, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3223"}, "Codelistqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}, "Relatedplacelocationone_04": {"maxLength": 70, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3222"}}, "x-openedi-composite-id": "C519"}, "C553": {"type": "object", "properties": {"Relatedplacelocationtwoidentification_01": {"maxLength": 25, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3233"}, "Codelistqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}, "Relatedplacelocationtwo_04": {"maxLength": 70, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3232"}}, "x-openedi-composite-id": "C553"}, "LOC": {"required": ["Placelocationqualifier_01"], "type": "object", "properties": {"Placelocationqualifier_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3227"}], "x-openedi-element-id": "3227"}, "LOCATIONIDENTIFICATION_02": {"$ref": "#/components/schemas/C517"}, "RELATEDLOCATIONONEIDENTIFICATION_03": {"$ref": "#/components/schemas/C519"}, "RELATEDLOCATIONTWOIDENTIFICATION_04": {"$ref": "#/components/schemas/C553"}, "Relationcoded_05": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "5479"}}, "x-openedi-segment-id": "LOC"}, "EDIFACT_ID_2005": {"enum": ["10", "101", "107", "108", "109", "11", "110", "111", "113", "114", "115", "117", "119", "12", "123", "124", "125", "126", "128", "129", "13", "131", "132", "133", "134", "135", "136", "137", "138", "14", "140", "141", "143", "144", "146", "147", "148", "149", "15", "150", "151", "152", "153", "154", "155", "156", "157", "158", "159", "16", "160", "161", "162", "163", "164", "165", "166", "167", "168", "169", "17", "170", "171", "172", "173", "174", "175", "176", "177", "178", "179", "18", "180", "181", "182", "183", "184", "185", "186", "187", "188", "189", "190", "191", "192", "193", "194", "195", "196", "197", "198", "199", "2", "20", "200", "201", "202", "203", "204", "205", "206", "207", "208", "209", "21", "210", "211", "212", "213", "214", "215", "216", "218", "219", "22", "221", "222", "223", "224", "225", "226", "227", "228", "229", "230", "231", "232", "233", "234", "235", "236", "237", "238", "239", "240", "242", "243", "244", "245", "246", "247", "248", "249", "250", "251", "252", "253", "254", "255", "257", "258", "259", "260", "261", "262", "263", "264", "265", "266", "267", "268", "270", "271", "272", "273", "274", "275", "276", "277", "278", "279", "280", "281", "282", "283", "284", "285", "286", "287", "288", "290", "291", "292", "293", "294", "295", "296", "297", "298", "299", "3", "300", "301", "302", "303", "304", "305", "306", "307", "308", "309", "310", "311", "312", "313", "314", "315", "316", "317", "318", "319", "320", "321", "322", "323", "324", "325", "326", "327", "328", "329", "330", "331", "332", "333", "334", "335", "336", "337", "338", "339", "340", "341", "342", "343", "344", "345", "346", "347", "348", "349", "35", "350", "351", "352", "353", "354", "355", "356", "357", "358", "359", "36", "360", "361", "362", "363", "364", "365", "366", "367", "368", "369", "37", "370", "371", "372", "373", "374", "375", "376", "377", "378", "379", "38", "380", "381", "382", "383", "384", "387", "388", "389", "39", "4", "42", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "55", "58", "59", "60", "61", "63", "64", "65", "67", "69", "7", "71", "72", "74", "75", "76", "79", "8", "81", "84", "85", "89", "9", "90", "91", "92", "93", "94", "95", "96", "97", "ZZZ"], "type": "string", "format": "EDIFACT_ID_2005"}, "EDIFACT_ID_2379": {"enum": ["101", "102", "103", "105", "106", "107", "108", "109", "110", "2", "201", "202", "203", "204", "3", "301", "302", "303", "304", "305", "306", "401", "402", "404", "405", "501", "502", "503", "600", "601", "602", "603", "604", "608", "609", "610", "613", "614", "615", "616", "701", "702", "703", "704", "705", "706", "707", "708", "709", "710", "711", "713", "715", "716", "717", "718", "801", "802", "803", "804", "805", "806", "807", "808", "809", "810", "811", "812", "813", "814"], "type": "string", "format": "EDIFACT_ID_2379"}, "C507": {"required": ["Datetimeperiodqualifier_01"], "type": "object", "properties": {"Datetimeperiodqualifier_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_2005"}], "x-openedi-element-id": "2005"}, "Datetimeperiod_02": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "2380"}, "Datetimeperiodformatqualifier_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_2379"}], "x-openedi-element-id": "2379"}}, "x-openedi-composite-id": "C507"}, "DTM": {"required": ["DATETIMEPERIOD_01"], "type": "object", "properties": {"DATETIMEPERIOD_01": {"$ref": "#/components/schemas/C507"}}, "x-openedi-segment-id": "DTM"}, "EDIFACT_ID_7365": {"enum": ["1", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "2", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "3", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "4", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "5", "50", "51", "52", "53", "54", "55", "56", "57", "58", "59", "6", "60", "61", "62", "63", "64", "65", "66", "67", "7", "8", "9", "ZZZ"], "type": "string", "format": "EDIFACT_ID_7365"}, "EDIFACT_ID_7187": {"enum": ["1", "10", "11", "12", "13", "14", "15", "16", "17", "2", "3", "4", "5", "6", "7", "8", "9"], "type": "string", "format": "EDIFACT_ID_7187"}, "C529": {"required": ["Processingindicatorcoded_01"], "type": "object", "properties": {"Processingindicatorcoded_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_7365"}], "x-openedi-element-id": "7365"}, "Codelistqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}, "Processtypeidentification_04": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_7187"}], "x-openedi-element-id": "7187"}}, "x-openedi-composite-id": "C529"}, "GIS": {"required": ["PROCESSINGINDICATOR_01"], "type": "object", "properties": {"PROCESSINGINDICATOR_01": {"$ref": "#/components/schemas/C529"}}, "x-openedi-segment-id": "GIS"}, "EDIFACT_ID_3035": {"enum": ["AA", "AB", "AE", "AF", "AG", "AH", "AI", "AJ", "AK", "AL", "AM", "AN", "AO", "AP", "AQ", "AR", "AT", "AU", "AV", "AW", "AX", "AY", "AZ", "B1", "B2", "BA", "BB", "BC", "BD", "BE", "BF", "BG", "BH", "BI", "BK", "BL", "BM", "BN", "BO", "BP", "BQ", "BS", "BT", "BU", "BV", "BW", "BX", "BY", "BZ", "C1", "C2", "CA", "CB", "CC", "CD", "CE", "CF", "CG", "CH", "CI", "CJ", "CK", "CL", "CM", "CN", "CO", "COP", "CP", "CPD", "CQ", "CR", "CS", "CT", "CU", "CV", "CW", "CX", "CY", "CZ", "DA", "DB", "DC", "DCP", "DD", "DE", "DF", "DG", "DH", "DI", "DJ", "DK", "DL", "DM", "DN", "DO", "DP", "DQ", "DR", "DS", "DT", "DU", "DV", "DW", "DX", "DY", "DZ", "EA", "EB", "EC", "ED", "EE", "EF", "EG", "EH", "EI", "EJ", "EK", "EL", "EM", "EN", "EO", "EP", "EQ", "ER", "ES", "ET", "EU", "EV", "EW", "EX", "EY", "EZ", "FA", "FB", "FC", "FD", "FE", "FF", "FG", "FH", "FJ", "FK", "FL", "FM", "FN", "FO", "FP", "FR", "FT", "FW", "FX", "FY", "FZ", "GA", "GB", "GC", "GD", "GE", "GF", "GG", "GH", "GI", "GJ", "GK", "GL", "GM", "GN", "GO", "GP", "GQ", "GR", "GS", "GT", "GU", "GV", "GW", "GX", "GY", "GZ", "HA", "HB", "HC", "I1", "I2", "IB", "IC", "ID", "IE", "IF", "IG", "IH", "II", "IJ", "IL", "IM", "IN", "IO", "IP", "IQ", "IR", "IS", "IT", "IU", "IV", "IW", "LA", "LB", "LN", "LP", "MA", "MF", "MG", "MI", "MP", "MR", "MS", "MT", "N1", "N2", "NI", "OA", "OB", "OF", "OI", "OO", "OP", "OR", "OS", "OT", "OV", "OY", "P1", "P2", "P3", "P4", "PA", "PB", "PC", "PD", "PE", "PF", "PG", "PH", "PI", "PJ", "PK", "PL", "PM", "PN", "PO", "PQ", "PR", "PS", "PT", "PW", "PX", "PY", "PZ", "RA", "RB", "RE", "RF", "RH", "RI", "RL", "RM", "RP", "RS", "RV", "RW", "SB", "SE", "SF", "SG", "SI", "SK", "SN", "SO", "SR", "SS", "ST", "SU", "SX", "SY", "SZ", "TC", "TCP", "TD", "TR", "TS", "TT", "UC", "UD", "UHP", "UP", "VN", "WD", "WH", "WM", "WPA", "WS", "XX", "ZZZ"], "type": "string", "format": "EDIFACT_ID_3035"}, "C078": {"type": "object", "properties": {"Accountholdernumber_01": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3194"}, "Accountholdername_02": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3192"}, "Accountholdername_03": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3192"}, "Currencycoded_04": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "6345"}}, "x-openedi-composite-id": "C078"}, "C088": {"type": "object", "properties": {"Institutionnameidentification_01": {"maxLength": 11, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3433"}, "Codelistqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}, "Institutionbranchnumber_04": {"maxLength": 17, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3434"}, "Codelistqualifier_05": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_06": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}, "Institutionname_07": {"maxLength": 70, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3432"}, "Institutionbranchplace_08": {"maxLength": 70, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3436"}}, "x-openedi-composite-id": "C088"}, "FII": {"required": ["Partyqualifier_01"], "type": "object", "properties": {"Partyqualifier_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3035"}], "x-openedi-element-id": "3035"}, "ACCOUNTIDENTIFICATION_02": {"$ref": "#/components/schemas/C078"}, "INSTITUTIONIDENTIFICATION_03": {"$ref": "#/components/schemas/C088"}, "Countrycoded_04": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3207"}}, "x-openedi-segment-id": "FII"}, "EDIFACT_ID_6311": {"enum": ["AAA", "AAB", "AAC", "AAD", "AAE", "AAF", "AAG", "AAH", "AAI", "AAJ", "AAK", "AAL", "AAM", "AAN", "AAO", "AAP", "AAQ", "AAR", "AAS", "AAT", "AAU", "AAV", "AAW", "AAX", "AAY", "AAZ", "ABA", "ABB", "ABC", "ABD", "ABE", "ABF", "ABG", "ABH", "ABI", "ABJ", "ABK", "ABL", "ABM", "ABN", "ABP", "ASW", "CH", "CHW", "CN", "CS", "CT", "DEN", "DR", "DT", "DV", "DX", "EGW", "EN", "EVO", "FO", "IV", "LAO", "LC", "LGL", "LL", "LMT", "NAX", "PAL", "PC", "PD", "PL", "PLL", "RL", "RN", "SE", "SH", "SM", "SO", "SPG", "SR", "ST", "SU", "SV", "TE", "TL", "TR", "TX", "VO", "VOL", "VT", "WT", "WX"], "type": "string", "format": "EDIFACT_ID_6311"}, "EDIFACT_ID_6313": {"enum": ["A", "AAA", "AAB", "AAC", "AAD", "AAE", "AAF", "AAG", "AAH", "AAI", "AAL", "AAM", "AAN", "AAO", "AAP", "AAQ", "AAR", "AAS", "AAT", "AAU", "AAV", "AAW", "AAX", "AAY", "AAZ", "ABA", "ABB", "ABC", "ABD", "ABE", "ABF", "ABG", "ABH", "ABI", "ABJ", "ABK", "ABL", "ABM", "ABN", "ABO", "ABQ", "ABR", "ABS", "ABU", "ABV", "ABW", "ABX", "ABY", "ABZ", "ACA", "ACB", "ACC", "ACD", "ACE", "ACF", "ACG", "ACH", "ACI", "ACJ", "ACK", "ACL", "ACM", "ACN", "ACO", "ACP", "ACQ", "ACR", "ACS", "ACT", "ACU", "ACV", "ACW", "ACX", "ACY", "ACZ", "ADA", "ADB", "ADC", "ADD", "ADE", "ADF", "ADG", "ADH", "ADI", "ADJ", "ADK", "ADL", "ADM", "ADN", "ADO", "ADP", "ADQ", "ADR", "ADS", "ADT", "ADU", "ADV", "ADW", "ADX", "ADY", "ADZ", "AEA", "AEB", "AEC", "AED", "AEE", "AEF", "AEG", "AEH", "AEI", "AEJ", "AEK", "AF", "B", "BL", "BND", "BR", "BRA", "BRE", "BS", "BSW", "BW", "CHN", "CM", "CT", "CV", "CZ", "D", "DI", "DL", "DN", "DP", "DR", "DS", "DW", "E", "EA", "F", "FI", "FL", "FN", "FV", "G", "GG", "GW", "HF", "HM", "HT", "IB", "ID", "L", "LM", "LN", "LND", "M", "MO", "MW", "N", "OD", "PRS", "PTN", "RA", "RF", "RJ", "RMW", "RP", "RUN", "RY", "SQ", "T", "TC", "TH", "TN", "TT", "U", "VH", "VW", "WA", "WD", "WM", "WT", "WU", "XH", "XQ", "XZ", "YS", "ZAL", "ZAS", "ZB", "ZBI", "ZC", "ZCA", "ZCB", "ZCE", "ZCL", "ZCO", "ZCR", "ZCU", "ZFE", "ZFS", "ZGE", "ZH", "ZK", "ZMG", "ZMN", "ZMO", "ZN", "ZNA", "ZNB", "ZNI", "ZO", "ZP", "ZPB", "ZS", "ZSB", "ZSE", "ZSI", "ZSL", "ZSN", "ZTA", "ZTE", "ZTI", "ZV", "ZW", "ZWA", "ZZN", "ZZR", "ZZZ"], "type": "string", "format": "EDIFACT_ID_6313"}, "EDIFACT_ID_6321": {"enum": ["10", "11", "12", "13", "15", "3", "4", "5", "6", "7", "8"], "type": "string", "format": "EDIFACT_ID_6321"}, "EDIFACT_ID_6155": {"enum": ["1", "10", "11", "12", "13", "14", "15", "16", "18", "19", "2", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "3", "32", "33", "34", "35", "36", "37", "38", "39", "4", "40", "41", "42", "43", "44", "45", "5", "6", "7", "8", "9"], "type": "string", "format": "EDIFACT_ID_6155"}, "C502": {"type": "object", "properties": {"Measurementdimensioncoded_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_6313"}], "x-openedi-element-id": "6313"}, "Measurementsignificancecoded_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_6321"}], "x-openedi-element-id": "6321"}, "Measurementattributeidentification_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_6155"}], "x-openedi-element-id": "6155"}, "Measurementattribute_04": {"maxLength": 70, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "6154"}}, "x-openedi-composite-id": "C502"}, "C174": {"required": ["Measureunitqualifier_01"], "type": "object", "properties": {"Measureunitqualifier_01": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "6411"}, "Measurementvalue_02": {"maxLength": 18, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "6314"}, "Rangeminimum_03": {"maxLength": 18, "minLength": 1, "type": "string", "format": "EDIFACT_N", "x-openedi-element-id": "6162"}, "Rangemaximum_04": {"maxLength": 18, "minLength": 1, "type": "string", "format": "EDIFACT_N", "x-openedi-element-id": "6152"}, "Significantdigits_05": {"maxLength": 2, "minLength": 1, "type": "string", "format": "EDIFACT_N", "x-openedi-element-id": "6432"}}, "x-openedi-composite-id": "C174"}, "EDIFACT_ID_7383": {"enum": ["1S", "2S", "AA", "AB", "AC", "AD", "AE", "BC", "BS", "BT", "DF", "FR", "IN", "LE", "OA", "OS", "OT", "RI", "RR", "ST", "TB", "TP", "TS", "UC"], "type": "string", "format": "EDIFACT_ID_7383"}, "MEA": {"required": ["Measurementapplicationqualifier_01"], "type": "object", "properties": {"Measurementapplicationqualifier_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_6311"}], "x-openedi-element-id": "6311"}, "MEASUREMENTDETAILS_02": {"$ref": "#/components/schemas/C502"}, "VALUERANGE_03": {"$ref": "#/components/schemas/C174"}, "Surfacelayerindicatorcoded_04": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_7383"}], "x-openedi-element-id": "7383"}}, "x-openedi-segment-id": "MEA"}, "EDIFACT_ID_8053": {"enum": ["AA", "AB", "AD", "AE", "BL", "BPN", "BPY", "BR", "BX", "CH", "CN", "DPA", "EFP", "EYP", "FPN", "FPR", "FSU", "LAR", "LU", "MPA", "PA", "PBP", "PFP", "PL", "PPA", "PST", "RF", "RG", "RGF", "RO", "RR", "SCA", "SCB", "SCC", "SFA", "SPP", "STR", "SW", "TE", "TP", "TS", "TSU", "UL"], "type": "string", "format": "EDIFACT_ID_8053"}, "C237": {"type": "object", "properties": {"Equipmentidentificationnumber_01": {"maxLength": 17, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "8260"}, "Codelistqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}, "Countrycoded_04": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3207"}}, "x-openedi-composite-id": "C237"}, "EDIFACT_ID_8155": {"enum": ["1", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "2", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "3", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "4", "40", "41", "42", "5", "6", "7", "8", "9"], "type": "string", "format": "EDIFACT_ID_8155"}, "C224": {"type": "object", "properties": {"Equipmentsizeandtypeidentification_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_8155"}], "x-openedi-element-id": "8155"}, "Codelistqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}, "Equipmentsizeandtype_04": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "8154"}}, "x-openedi-composite-id": "C224"}, "EDIFACT_ID_8077": {"enum": ["1", "2", "3", "4", "5"], "type": "string", "format": "EDIFACT_ID_8077"}, "EDIFACT_ID_8249": {"enum": ["1", "2", "3", "4", "5", "6", "7", "8"], "type": "string", "format": "EDIFACT_ID_8249"}, "EDIFACT_ID_8169": {"enum": ["1", "2", "3", "4", "5", "6", "7", "8"], "type": "string", "format": "EDIFACT_ID_8169"}, "EQD": {"required": ["Equipmentqualifier_01"], "type": "object", "properties": {"Equipmentqualifier_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_8053"}], "x-openedi-element-id": "8053"}, "EQUIPMENTIDENTIFICATION_02": {"$ref": "#/components/schemas/C237"}, "EQUIPMENTSIZEANDTYPE_03": {"$ref": "#/components/schemas/C224"}, "Equipmentsuppliercoded_04": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_8077"}], "x-openedi-element-id": "8077"}, "Equipmentstatuscoded_05": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_8249"}], "x-openedi-element-id": "8249"}, "Fullemptyindicatorcoded_06": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_8169"}], "x-openedi-element-id": "8169"}}, "x-openedi-segment-id": "EQD"}, "EDIFACT_ID_9303": {"enum": ["AA", "AB", "CA", "CU", "SH", "TO"], "type": "string", "format": "EDIFACT_ID_9303"}, "C215": {"type": "object", "properties": {"Sealingpartycoded_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_9303"}], "x-openedi-element-id": "9303"}, "Codelistqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}, "Sealingparty_04": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "9302"}}, "x-openedi-composite-id": "C215"}, "EDIFACT_ID_4517": {"enum": ["1", "2"], "type": "string", "format": "EDIFACT_ID_4517"}, "SEL": {"required": ["Sealnumber_01"], "type": "object", "properties": {"Sealnumber_01": {"maxLength": 10, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "9308"}, "SEALISSUER_02": {"$ref": "#/components/schemas/C215"}, "Sealconditioncoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_4517"}], "x-openedi-element-id": "4517"}}, "x-openedi-segment-id": "SEL"}, "EDIFACT_ID_4451": {"enum": ["AAA", "AAB", "AAC", "AAD", "AAE", "AAF", "AAG", "AAH", "AAI", "AAJ", "AAK", "AAL", "AAM", "AAN", "AAO", "AAP", "AAQ", "AAR", "AAS", "AAT", "AAU", "AAV", "AAW", "AAX", "AAY", "AAZ", "ABA", "ABC", "ABD", "ABE", "ABF", "ABG", "ABH", "ABI", "ABJ", "ABK", "ABL", "ABM", "ABN", "ABO", "ABP", "ABQ", "ABR", "ABS", "ABT", "ABU", "ABV", "ABW", "ABX", "ABY", "ABZ", "ACA", "ACB", "ACC", "ACD", "ACE", "ACF", "ACG", "ACH", "ACI", "ACJ", "ACK", "ACL", "ACM", "ACN", "ACO", "ACP", "ACQ", "ACR", "ACS", "ACT", "ACU", "ACV", "ACW", "ACX", "ACY", "ACZ", "ADA", "ADB", "ADC", "ADE", "ADF", "ADG", "ADH", "ADI", "ADJ", "ADK", "ADL", "ADM", "ADN", "ADO", "ALC", "ALL", "ARR", "AUT", "BLC", "BLR", "CCI", "CEX", "CHG", "CIP", "CLP", "CLR", "COI", "CUR", "CUS", "DAR", "DCL", "DEL", "DIN", "DOC", "DUT", "EUR", "FBC", "GBL", "GEN", "GS7", "HAN", "HAZ", "ICN", "IIN", "IMI", "IND", "INS", "INV", "IRP", "ITR", "ITS", "LIN", "LOI", "MCO", "MKS", "ORI", "OSI", "PAC", "PAI", "PAY", "PKG", "PKT", "PMD", "PMT", "PRD", "PRF", "PRI", "PUR", "QIN", "QQD", "QUT", "RAH", "REG", "RET", "REV", "RQR", "RQT", "SAF", "SIC", "SIN", "SLR", "SPA", "SPG", "SPH", "SPP", "SPT", "SRN", "SSR", "SUR", "TCA", "TDT", "TRA", "TRR", "TXD", "WHI", "ZZZ"], "type": "string", "format": "EDIFACT_ID_4451"}, "EDIFACT_ID_4453": {"enum": ["1", "2", "3", "4"], "type": "string", "format": "EDIFACT_ID_4453"}, "C107": {"required": ["Freetextidentification_01"], "type": "object", "properties": {"Freetextidentification_01": {"maxLength": 17, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "4441"}, "Codelistqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}}, "x-openedi-composite-id": "C107"}, "C108": {"required": ["Freetext_01"], "type": "object", "properties": {"Freetext_01": {"maxLength": 70, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "4440"}, "Freetext_02": {"maxLength": 70, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "4440"}, "Freetext_03": {"maxLength": 70, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "4440"}, "Freetext_04": {"maxLength": 70, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "4440"}, "Freetext_05": {"maxLength": 70, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "4440"}}, "x-openedi-composite-id": "C108"}, "FTX": {"required": ["Textsubjectqualifier_01"], "type": "object", "properties": {"Textsubjectqualifier_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_4451"}], "x-openedi-element-id": "4451"}, "Textfunctioncoded_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_4453"}], "x-openedi-element-id": "4453"}, "TEXTREFERENCE_03": {"$ref": "#/components/schemas/C107"}, "TEXTLITERAL_04": {"$ref": "#/components/schemas/C108"}, "Languagecoded_05": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3453"}}, "x-openedi-segment-id": "FTX"}, "EDIFACT_ID_1153": {"enum": ["AAA", "AAB", "AAC", "AAD", "AAE", "AAG", "AAJ", "AAK", "AAL", "AAM", "AAN", "AAO", "AAP", "AAQ", "AAS", "AAT", "AAU", "AAV", "AAY", "AAZ", "ABA", "ABB", "ABC", "ABD", "ABE", "ABG", "ABH", "ABI", "ABJ", "ABK", "ABL", "ABO", "ABP", "ABQ", "ABR", "ABS", "ABT", "ABU", "ABW", "ABX", "ABY", "ABZ", "AC", "ACB", "ACC", "ACD", "ACE", "ACF", "ACG", "ACH", "ACI", "ACJ", "ACK", "ACL", "ACM", "ACN", "ACO", "ACP", "ACQ", "ACR", "ACS", "ACT", "ACU", "ACW", "ACX", "ACY", "ACZ", "ADA", "ADB", "ADC", "ADD", "ADE", "ADF", "ADG", "ADH", "ADI", "ADJ", "ADK", "ADP", "ADQ", "ADR", "ADS", "ADT", "ADU", "ADV", "ADW", "ADY", "ADZ", "AE", "AEA", "AEB", "AEC", "AED", "AEE", "AEF", "AEG", "AEH", "AEI", "AEJ", "AEK", "AEL", "AEM", "AEN", "AEO", "AEP", "AEQ", "AER", "AES", "AET", "AEU", "AEV", "AEW", "AEX", "AEY", "AEZ", "AF", "AFA", "AFB", "AFC", "AFD", "AFE", "AFF", "AFG", "AFI", "AFJ", "AFK", "AFL", "AFM", "AFN", "AFO", "AFP", "AFQ", "AFR", "AFS", "AFT", "AFU", "AFV", "AFW", "AFX", "AFY", "AFZ", "AGA", "AGB", "AGC", "AGD", "AGE", "AGF", "AGG", "AGH", "AGI", "AGJ", "AGK", "AGL", "AGM", "AGN", "AGO", "AGP", "AGQ", "AGR", "AGS", "AGT", "AGU", "AGV", "AGW", "AGX", "AGY", "AGZ", "AHA", "AHB", "AHC", "AHD", "AHE", "AHF", "AHG", "AHH", "AHI", "AHJ", "AHK", "AHL", "AHM", "AHN", "AHO", "AHP", "AHQ", "AHR", "AHS", "AHT", "AHU", "AHV", "AHW", "AHX", "AHY", "AHZ", "AIA", "AIB", "AIC", "AID", "AIE", "AIF", "AIG", "AIH", "AII", "AIJ", "AIK", "AIL", "AIM", "AIN", "AIO", "AIP", "AIQ", "AIR", "AIS", "AIT", "AIU", "AIV", "AIW", "AIX", "AIY", "AJA", "ALA", "ALB", "ALC", "ALD", "ALE", "ALF", "ALG", "ALH", "ALI", "ALJ", "ALK", "ALL", "ALM", "ALN", "ALO", "ALP", "ALQ", "ALR", "ALS", "ALT", "ALU", "ALV", "ALW", "ALX", "ALY", "ALZ", "AMA", "AMB", "AMC", "AMD", "AME", "AMF", "AMG", "AMI", "AMJ", "AMK", "AMN", "AMQ", "AMR", "AMS", "AMT", "AMU", "AP", "ASC", "AU", "AV", "AWB", "BA", "BC", "BD", "BE", "BH", "BM", "BN", "BO", "BR", "BT", "BW", "CAS", "CD", "CEC", "CFE", "CFO", "CG", "CH", "CK", "CKN", "CM", "CMR", "CN", "CNO", "CO", "COF", "CP", "CR", "CRN", "CS", "CST", "CT", "CU", "CV", "CW", "CZ", "DA", "DAN", "DB", "DI", "DL", "DM", "DQ", "DR", "EA", "EB", "ED", "EE", "EI", "EN", "EP", "EQ", "ER", "ERN", "ET", "EX", "FC", "FF", "FI", "FLW", "FN", "FO", "FS", "FT", "FV", "FX", "GA", "GC", "GD", "GDN", "GN", "HS", "HWB", "IA", "IB", "ICA", "ICE", "ICO", "II", "IL", "INB", "INN", "INO", "IP", "IS", "IT", "IV", "JB", "JE", "LA", "LAN", "LAR", "LB", "LC", "LI", "LO", "LS", "MA", "MB", "MF", "MG", "MH", "MR", "MRN", "MS", "MSS", "MWB", "NA", "OH", "OI", "ON", "OP", "OR", "PB", "PC", "PD", "PE", "PF", "PI", "PK", "PL", "POR", "PP", "PQ", "PR", "PS", "PW", "PY", "RA", "RC", "RCN", "RE", "REN", "RF", "RR", "RT", "SA", "SB", "SD", "SE", "SF", "SH", "SI", "SM", "SN", "SP", "SQ", "SRN", "SS", "STA", "SW", "SZ", "TB", "TE", "TF", "TI", "TL", "TN", "TP", "UAR", "UC", "UCN", "UN", "UO", "VA", "VC", "VM", "VN", "VON", "VP", "VR", "VS", "VT", "VV", "WE", "WM", "WN", "WR", "WS", "WY", "XA", "XC", "XP", "ZZZ"], "type": "string", "format": "EDIFACT_ID_1153"}, "C506": {"required": ["Referencequalifier_01"], "type": "object", "properties": {"Referencequalifier_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1153"}], "x-openedi-element-id": "1153"}, "Referencenumber_02": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "1154"}, "Linenumber_03": {"maxLength": 6, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "1156"}, "Referenceversionnumber_04": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "4000"}}, "x-openedi-composite-id": "C506"}, "RFF": {"required": ["REFERENCE_01"], "type": "object", "properties": {"REFERENCE_01": {"$ref": "#/components/schemas/C506"}}, "x-openedi-segment-id": "RFF"}, "EDIFACT_ID_7075": {"enum": ["1", "2", "3", "4"], "type": "string", "format": "EDIFACT_ID_7075"}, "EDIFACT_ID_7233": {"enum": ["34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "50", "51", "52", "53", "54", "55", "60", "61", "62", "63", "66", "67", "68", "69"], "type": "string", "format": "EDIFACT_ID_7233"}, "EDIFACT_ID_7073": {"enum": ["1", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "2", "20", "21", "22", "23", "3", "4", "5", "6", "7", "8", "9"], "type": "string", "format": "EDIFACT_ID_7073"}, "C531": {"type": "object", "properties": {"Packaginglevelcoded_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_7075"}], "x-openedi-element-id": "7075"}, "Packagingrelatedinformationcoded_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_7233"}], "x-openedi-element-id": "7233"}, "Packagingtermsandconditionscoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_7073"}], "x-openedi-element-id": "7073"}}, "x-openedi-composite-id": "C531"}, "C202": {"type": "object", "properties": {"Typeofpackagesidentification_01": {"maxLength": 17, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "7065"}, "Codelistqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}, "Typeofpackages_04": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "7064"}}, "x-openedi-composite-id": "C202"}, "EDIFACT_ID_7077": {"enum": ["A", "B", "C", "D", "E", "F", "S", "X"], "type": "string", "format": "EDIFACT_ID_7077"}, "EDIFACT_ID_7143": {"enum": ["AA", "AB", "AC", "AD", "AE", "AF", "AG", "AH", "AI", "AJ", "AK", "AL", "AM", "AN", "AO", "AP", "AT", "AU", "BO", "BP", "CC", "CG", "CL", "CR", "CV", "DR", "DW", "EC", "EF", "EN", "GB", "GN", "GS", "HS", "IB", "IN", "IS", "IT", "IZ", "MA", "MF", "MN", "MP", "NB", "ON", "PD", "PL", "PO", "PV", "QS", "RC", "RN", "RU", "RY", "SA", "SG", "SK", "SN", "SRS", "SS", "ST", "TG", "UA", "UP", "VN", "VP", "VS", "VX", "ZZZ"], "type": "string", "format": "EDIFACT_ID_7143"}, "C402": {"required": ["Itemdescriptiontypecoded_01", "Typeofpackages_02"], "type": "object", "properties": {"Itemdescriptiontypecoded_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_7077"}], "x-openedi-element-id": "7077"}, "Typeofpackages_02": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "7064"}, "Itemnumbertypecoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_7143"}], "x-openedi-element-id": "7143"}, "Typeofpackages_04": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "7064"}, "Itemnumbertypecoded_05": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_7143"}], "x-openedi-element-id": "7143"}}, "x-openedi-composite-id": "C402"}, "EDIFACT_ID_8395": {"enum": ["1", "2", "3", "ZZZ"], "type": "string", "format": "EDIFACT_ID_8395"}, "EDIFACT_ID_8393": {"enum": ["1", "10", "2", "3", "4", "5", "6", "7", "8", "9"], "type": "string", "format": "EDIFACT_ID_8393"}, "C532": {"type": "object", "properties": {"Returnablepackagefreightpaymentresponsibilitycoded_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_8395"}], "x-openedi-element-id": "8395"}, "Returnablepackageloadcontentscoded_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_8393"}], "x-openedi-element-id": "8393"}}, "x-openedi-composite-id": "C532"}, "PAC": {"type": "object", "properties": {"Numberofpackages_01": {"maxLength": 8, "minLength": 1, "type": "string", "format": "EDIFACT_N", "x-openedi-element-id": "7224"}, "PACKAGINGDETAILS_02": {"$ref": "#/components/schemas/C531"}, "PACKAGETYPE_03": {"$ref": "#/components/schemas/C202"}, "PACKAGETYPEIDENTIFICATION_04": {"$ref": "#/components/schemas/C402"}, "RETURNABLEPACKAGEDETAILS_05": {"$ref": "#/components/schemas/C532"}}, "x-openedi-segment-id": "PAC"}, "EDIFACT_ID_4233": {"enum": ["1", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "2", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "3", "30", "4", "5", "6", "7", "8", "9", "ZZZ"], "type": "string", "format": "EDIFACT_ID_4233"}, "C210": {"required": ["Shippingmarks_01"], "type": "object", "properties": {"Shippingmarks_01": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "7102"}, "Shippingmarks_02": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "7102"}, "Shippingmarks_03": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "7102"}, "Shippingmarks_04": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "7102"}, "Shippingmarks_05": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "7102"}, "Shippingmarks_06": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "7102"}, "Shippingmarks_07": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "7102"}, "Shippingmarks_08": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "7102"}, "Shippingmarks_09": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "7102"}, "Shippingmarks_10": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "7102"}}, "x-openedi-composite-id": "C210"}, "EDIFACT_ID_8275": {"enum": ["1", "2", "3", "4", "5", "6", "7", "8"], "type": "string", "format": "EDIFACT_ID_8275"}, "C827": {"required": ["Typeofmarkingcoded_01"], "type": "object", "properties": {"Typeofmarkingcoded_01": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "7511"}, "Codelistqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}}, "x-openedi-composite-id": "C827"}, "PCI": {"type": "object", "properties": {"Markinginstructionscoded_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_4233"}], "x-openedi-element-id": "4233"}, "MARKSLABELS_02": {"$ref": "#/components/schemas/C210"}, "Containerpackagestatuscoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_8275"}], "x-openedi-element-id": "8275"}, "TYPEOFMARKING_04": {"$ref": "#/components/schemas/C827"}}, "x-openedi-segment-id": "PCI"}, "Loop_PCI_CUSDEC": {"required": ["PCI"], "type": "object", "properties": {"PCI": {"$ref": "#/components/schemas/PCI"}, "FTX": {"$ref": "#/components/schemas/FTX"}}, "x-openedi-loop-id": "PCI"}, "Loop_PAC_CUSDEC": {"required": ["PAC"], "type": "object", "properties": {"PAC": {"$ref": "#/components/schemas/PAC"}, "PCILoop": {"maxItems": 999, "type": "array", "items": {"$ref": "#/components/schemas/Loop_PCI_CUSDEC"}}}, "x-openedi-loop-id": "PAC"}, "Loop_RFF_CUSDEC": {"required": ["RFF"], "type": "object", "properties": {"RFF": {"$ref": "#/components/schemas/RFF"}, "DTM": {"$ref": "#/components/schemas/DTM"}, "PACLoop": {"maxItems": 10, "type": "array", "items": {"$ref": "#/components/schemas/Loop_PAC_CUSDEC"}}}, "x-openedi-loop-id": "RFF"}, "EDIFACT_ID_8051": {"enum": ["1", "10", "11", "12", "13", "14", "2", "20", "21", "22", "23", "24", "25", "26", "27", "28", "30"], "type": "string", "format": "EDIFACT_ID_8051"}, "C220": {"type": "object", "properties": {"Modeoftransportcoded_01": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "8067"}, "Modeoftransport_02": {"maxLength": 17, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "8066"}}, "x-openedi-composite-id": "C220"}, "EDIFACT_ID_8179": {"enum": ["1", "11", "12", "13", "2", "21", "22", "23", "24", "25", "3", "31", "32", "33", "35", "36", "37", "38", "4", "5", "6", "7", "9"], "type": "string", "format": "EDIFACT_ID_8179"}, "C228": {"type": "object", "properties": {"Typeofmeansoftransportidentification_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_8179"}], "x-openedi-element-id": "8179"}, "Typeofmeansoftransport_02": {"maxLength": 17, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "8178"}}, "x-openedi-composite-id": "C228"}, "C040": {"type": "object", "properties": {"Carrieridentification_01": {"maxLength": 17, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3127"}, "Codelistqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}, "Carriername_04": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3128"}}, "x-openedi-composite-id": "C040"}, "EDIFACT_ID_8101": {"enum": ["BS", "SB", "SC", "SD", "SF", "SS", "ZZZ"], "type": "string", "format": "EDIFACT_ID_8101"}, "EDIFACT_ID_8457": {"enum": ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "P", "R", "T", "U", "V", "W", "X", "Y", "ZZZ"], "type": "string", "format": "EDIFACT_ID_8457"}, "EDIFACT_ID_8459": {"enum": ["A", "B", "S", "X", "ZZZ"], "type": "string", "format": "EDIFACT_ID_8459"}, "C401": {"required": ["Excesstransportationreasoncoded_01", "Excesstransportationresponsibilitycoded_02"], "type": "object", "properties": {"Excesstransportationreasoncoded_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_8457"}], "x-openedi-element-id": "8457"}, "Excesstransportationresponsibilitycoded_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_8459"}], "x-openedi-element-id": "8459"}, "Customerauthorizationnumber_03": {"maxLength": 17, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "7130"}}, "x-openedi-composite-id": "C401"}, "C222": {"type": "object", "properties": {"Idofmeansoftransportidentification_01": {"maxLength": 9, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "8213"}, "Codelistqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}, "Idofthemeansoftransport_04": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "8212"}, "Nationalityofmeansoftransportcoded_05": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "8453"}}, "x-openedi-composite-id": "C222"}, "EDIFACT_ID_8281": {"enum": ["1", "2"], "type": "string", "format": "EDIFACT_ID_8281"}, "TDT": {"required": ["Transportstagequalifier_01"], "type": "object", "properties": {"Transportstagequalifier_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_8051"}], "x-openedi-element-id": "8051"}, "Conveyancereferencenumber_02": {"maxLength": 17, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "8028"}, "MODEOFTRANSPORT_03": {"$ref": "#/components/schemas/C220"}, "TRANSPORTMEANS_04": {"$ref": "#/components/schemas/C228"}, "CARRIER_05": {"$ref": "#/components/schemas/C040"}, "Transitdirectioncoded_06": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_8101"}], "x-openedi-element-id": "8101"}, "EXCESSTRANSPORTATIONINFORMATION_07": {"$ref": "#/components/schemas/C401"}, "TRANSPORTIDENTIFICATION_08": {"$ref": "#/components/schemas/C222"}, "Transportownershipcoded_09": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_8281"}], "x-openedi-element-id": "8281"}}, "x-openedi-segment-id": "TDT"}, "TPL": {"required": ["TRANSPORTIDENTIFICATION_01"], "type": "object", "properties": {"TRANSPORTIDENTIFICATION_01": {"$ref": "#/components/schemas/C222"}}, "x-openedi-segment-id": "TPL"}, "Loop_TDT_CUSDEC": {"required": ["TDT"], "type": "object", "properties": {"TDT": {"$ref": "#/components/schemas/TDT"}, "TPL": {"$ref": "#/components/schemas/TPL"}}, "x-openedi-loop-id": "TDT"}, "EDIFACT_ID_1373": {"enum": ["1", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "2", "20", "21", "3", "4", "5", "6", "7", "8", "9"], "type": "string", "format": "EDIFACT_ID_1373"}, "C503": {"type": "object", "properties": {"Documentmessagenumber_01": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "1004"}, "Documentmessagestatuscoded_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1373"}], "x-openedi-element-id": "1373"}, "Documentmessagesource_03": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "1366"}, "Languagecoded_04": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3453"}}, "x-openedi-composite-id": "C503"}, "EDIFACT_ID_3153": {"enum": ["AA", "AB", "AC", "CA", "EI", "EM", "EX", "FT", "FX", "GM", "IE", "IM", "MA", "PB", "PS", "SW", "TE", "TG", "TL", "TM", "TT", "TX", "XF"], "type": "string", "format": "EDIFACT_ID_3153"}, "DOC": {"required": ["DOCUMENTMESSAGENAME_01"], "type": "object", "properties": {"DOCUMENTMESSAGENAME_01": {"$ref": "#/components/schemas/C002"}, "DOCUMENTMESSAGEDETAILS_02": {"$ref": "#/components/schemas/C503"}, "Communicationchannelidentifiercoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3153"}], "x-openedi-element-id": "3153"}, "Numberofcopiesofdocumentrequired_04": {"maxLength": 2, "minLength": 1, "type": "string", "format": "EDIFACT_N", "x-openedi-element-id": "1220"}, "Numberoforiginalsofdocumentrequired_05": {"maxLength": 2, "minLength": 1, "type": "string", "format": "EDIFACT_N", "x-openedi-element-id": "1218"}}, "x-openedi-segment-id": "DOC"}, "Loop_DOC_CUSDEC": {"required": ["DOC"], "type": "object", "properties": {"DOC": {"$ref": "#/components/schemas/DOC"}, "DTM": {"maxItems": 2, "type": "array", "items": {"$ref": "#/components/schemas/DTM"}}, "LOC": {"$ref": "#/components/schemas/LOC"}}, "x-openedi-loop-id": "DOC"}, "C082": {"required": ["Partyididentification_01"], "type": "object", "properties": {"Partyididentification_01": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3039"}, "Codelistqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}}, "x-openedi-composite-id": "C082"}, "C058": {"required": ["Nameandaddressline_01"], "type": "object", "properties": {"Nameandaddressline_01": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3124"}, "Nameandaddressline_02": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3124"}, "Nameandaddressline_03": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3124"}, "Nameandaddressline_04": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3124"}, "Nameandaddressline_05": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3124"}}, "x-openedi-composite-id": "C058"}, "EDIFACT_ID_3045": {"enum": ["1"], "type": "string", "format": "EDIFACT_ID_3045"}, "C080": {"required": ["Partyname_01"], "type": "object", "properties": {"Partyname_01": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3036"}, "Partyname_02": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3036"}, "Partyname_03": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3036"}, "Partyname_04": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3036"}, "Partyname_05": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3036"}, "Partynameformatcoded_06": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3045"}], "x-openedi-element-id": "3045"}}, "x-openedi-composite-id": "C080"}, "C059": {"required": ["Streetandnumberpobox_01"], "type": "object", "properties": {"Streetandnumberpobox_01": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3042"}, "Streetandnumberpobox_02": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3042"}, "Streetandnumberpobox_03": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3042"}, "Streetandnumberpobox_04": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3042"}}, "x-openedi-composite-id": "C059"}, "NAD": {"required": ["Partyqualifier_01"], "type": "object", "properties": {"Partyqualifier_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3035"}], "x-openedi-element-id": "3035"}, "PARTYIDENTIFICATIONDETAILS_02": {"$ref": "#/components/schemas/C082"}, "NAMEANDADDRESS_03": {"$ref": "#/components/schemas/C058"}, "PARTYNAME_04": {"$ref": "#/components/schemas/C080"}, "STREET_05": {"$ref": "#/components/schemas/C059"}, "Cityname_06": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3164"}, "Countrysubentityidentification_07": {"maxLength": 9, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3229"}, "Postcodeidentification_08": {"maxLength": 9, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3251"}, "Countrycoded_09": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3207"}}, "x-openedi-segment-id": "NAD"}, "EDIFACT_ID_3139": {"enum": ["AA", "AB", "AC", "AD", "AE", "AF", "AG", "AH", "AI", "AJ", "AK", "AL", "AM", "AN", "AO", "AP", "AQ", "AR", "AS", "AT", "AU", "AV", "AW", "AX", "AY", "AZ", "BA", "BB", "BC", "BD", "BE", "BF", "BU", "CA", "CB", "CC", "CD", "CE", "CF", "CG", "CN", "CO", "CP", "CR", "CW", "DE", "DI", "DL", "EB", "EC", "ED", "EX", "GR", "HE", "HG", "HM", "IC", "IN", "LB", "LO", "MC", "MD", "MH", "MR", "MS", "NT", "OC", "PA", "PD", "PE", "PM", "QA", "QC", "RD", "SA", "SC", "SD", "SR", "SU", "TA", "TD", "TI", "TR", "WH", "ZZZ"], "type": "string", "format": "EDIFACT_ID_3139"}, "C056": {"type": "object", "properties": {"Departmentoremployeeidentification_01": {"maxLength": 17, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3413"}, "Departmentoremployee_02": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3412"}}, "x-openedi-composite-id": "C056"}, "CTA": {"type": "object", "properties": {"Contactfunctioncoded_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3139"}], "x-openedi-element-id": "3139"}, "DEPARTMENTOREMPLOYEEDETAILS_02": {"$ref": "#/components/schemas/C056"}}, "x-openedi-segment-id": "CTA"}, "EDIFACT_ID_3155": {"enum": ["AA", "AB", "AC", "AD", "AE", "CA", "EI", "EM", "EX", "FT", "FX", "GM", "IE", "IM", "MA", "PB", "PS", "SW", "TE", "TG", "TL", "TM", "TT", "TX", "XF"], "type": "string", "format": "EDIFACT_ID_3155"}, "C076": {"required": ["Communicationchannelqualifier_02", "Communicationnumber_01"], "type": "object", "properties": {"Communicationnumber_01": {"maxLength": 512, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3148"}, "Communicationchannelqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3155"}], "x-openedi-element-id": "3155"}}, "x-openedi-composite-id": "C076"}, "COM": {"required": ["COMMUNICATIONCONTACT_01"], "type": "object", "properties": {"COMMUNICATIONCONTACT_01": {"$ref": "#/components/schemas/C076"}}, "x-openedi-segment-id": "COM"}, "Loop_NAD_CUSDEC": {"required": ["NAD"], "type": "object", "properties": {"NAD": {"$ref": "#/components/schemas/NAD"}, "RFF": {"maxItems": 10, "type": "array", "items": {"$ref": "#/components/schemas/RFF"}}, "CTA": {"$ref": "#/components/schemas/CTA"}, "COM": {"maxItems": 5, "type": "array", "items": {"$ref": "#/components/schemas/COM"}}}, "x-openedi-loop-id": "NAD"}, "EDIFACT_ID_4055": {"enum": ["1", "2", "3", "4", "5", "6"], "type": "string", "format": "EDIFACT_ID_4055"}, "EDIFACT_ID_4215": {"enum": ["A", "CA", "CC", "CF", "DF", "FO", "IC", "MX", "NC", "NS", "PA", "PB", "PC", "PE", "PO", "PP", "PU", "RC", "RF", "RS", "TP", "WC", "ZZZ"], "type": "string", "format": "EDIFACT_ID_4215"}, "C100": {"type": "object", "properties": {"Termsofdeliveryortransportcoded_01": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "4053"}, "Codelistqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}, "Termsofdeliveryortransport_04": {"maxLength": 70, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "4052"}, "Termsofdeliveryortransport_05": {"maxLength": 70, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "4052"}}, "x-openedi-composite-id": "C100"}, "TOD": {"type": "object", "properties": {"Termsofdeliveryortransportfunctioncoded_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_4055"}], "x-openedi-element-id": "4055"}, "Transportchargesmethodofpaymentcoded_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_4215"}], "x-openedi-element-id": "4215"}, "TERMSOFDELIVERYORTRANSPORT_03": {"$ref": "#/components/schemas/C100"}}, "x-openedi-segment-id": "TOD"}, "Loop_TOD_CUSDEC": {"required": ["TOD"], "type": "object", "properties": {"TOD": {"$ref": "#/components/schemas/TOD"}, "LOC": {"maxItems": 2, "type": "array", "items": {"$ref": "#/components/schemas/LOC"}}, "FTX": {"$ref": "#/components/schemas/FTX"}}, "x-openedi-loop-id": "TOD"}, "EDIFACT_ID_5025": {"enum": ["1", "10", "100", "101", "102", "103", "104", "105", "106", "107", "108", "109", "11", "110", "111", "112", "113", "114", "115", "116", "117", "118", "119", "12", "120", "121", "122", "123", "124", "125", "126", "127", "128", "129", "13", "130", "131", "132", "133", "134", "135", "136", "138", "139", "14", "140", "141", "142", "143", "144", "145", "146", "147", "148", "149", "15", "150", "151", "152", "153", "154", "155", "156", "157", "158", "159", "16", "160", "161", "162", "163", "164", "165", "166", "167", "168", "169", "17", "170", "171", "172", "173", "174", "175", "176", "177", "178", "179", "18", "180", "181", "182", "183", "184", "185", "186", "187", "188", "189", "19", "190", "192", "193", "194", "195", "196", "197", "198", "199", "2", "20", "200", "201", "202", "203", "204", "205", "206", "207", "208", "209", "21", "210", "211", "212", "213", "214", "215", "216", "217", "218", "219", "22", "220", "221", "222", "223", "224", "225", "226", "227", "228", "229", "23", "230", "231", "232", "233", "234", "235", "236", "237", "238", "239", "24", "240", "241", "242", "243", "244", "245", "246", "247", "248", "249", "25", "250", "251", "252", "253", "254", "255", "256", "257", "258", "259", "26", "260", "261", "262", "263", "264", "265", "266", "267", "268", "269", "27", "270", "271", "272", "273", "274", "275", "276", "277", "278", "279", "28", "280", "281", "282", "283", "284", "285", "286", "287", "288", "289", "29", "290", "291", "292", "293", "294", "295", "296", "297", "298", "299", "3", "30", "300", "301", "302", "303", "304", "305", "306", "307", "308", "309", "31", "310", "311", "312", "313", "314", "315", "316", "317", "318", "319", "32", "320", "321", "322", "323", "324", "325", "326", "327", "328", "329", "33", "330", "331", "332", "333", "334", "335", "336", "338", "339", "34", "340", "341", "342", "35", "36", "37", "38", "39", "4", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "5", "50", "51", "52", "53", "54", "55", "56", "57", "58", "59", "6", "60", "61", "62", "63", "64", "65", "66", "67", "68", "69", "7", "70", "71", "72", "73", "74", "75", "76", "77", "78", "79", "8", "80", "81", "82", "83", "84", "85", "86", "87", "88", "89", "9", "90", "91", "92", "93", "94", "95", "96", "97", "98", "99", "ZZZ"], "type": "string", "format": "EDIFACT_ID_5025"}, "EDIFACT_ID_6343": {"enum": ["1", "10", "11", "12", "13", "14", "15", "16", "17", "2", "3", "4", "5", "6", "7", "8", "9"], "type": "string", "format": "EDIFACT_ID_6343"}, "EDIFACT_ID_4405": {"enum": ["1", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "2", "20", "21", "3", "4", "5", "6", "7", "8", "9"], "type": "string", "format": "EDIFACT_ID_4405"}, "C516": {"required": ["Monetaryamounttypequalifier_01"], "type": "object", "properties": {"Monetaryamounttypequalifier_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_5025"}], "x-openedi-element-id": "5025"}, "Monetaryamount_02": {"maxLength": 18, "minLength": 1, "type": "string", "format": "EDIFACT_N", "x-openedi-element-id": "5004"}, "Currencycoded_03": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "6345"}, "Currencyqualifier_04": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_6343"}], "x-openedi-element-id": "6343"}, "Statuscoded_05": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_4405"}], "x-openedi-element-id": "4405"}}, "x-openedi-composite-id": "C516"}, "MOA": {"required": ["MONETARYAMOUNT_01"], "type": "object", "properties": {"MONETARYAMOUNT_01": {"$ref": "#/components/schemas/C516"}}, "x-openedi-segment-id": "MOA"}, "EDIFACT_ID_6347": {"enum": ["1", "2", "3", "4", "5", "6", "7"], "type": "string", "format": "EDIFACT_ID_6347"}, "C504": {"required": ["Currencydetailsqualifier_01"], "type": "object", "properties": {"Currencydetailsqualifier_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_6347"}], "x-openedi-element-id": "6347"}, "Currencycoded_02": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "6345"}, "Currencyqualifier_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_6343"}], "x-openedi-element-id": "6343"}, "Currencyratebase_04": {"maxLength": 4, "minLength": 1, "type": "string", "format": "EDIFACT_N", "x-openedi-element-id": "6348"}}, "x-openedi-composite-id": "C504"}, "EDIFACT_ID_6341": {"enum": ["AAA", "AMS", "ARG", "AST", "AUS", "BEL", "CAN", "CAR", "CIE", "DEN", "ECR", "FIN", "FRA", "IMF", "LNF", "LNS", "MIL", "NOR", "NYC", "PHI", "SRE", "SWE", "ZUR"], "type": "string", "format": "EDIFACT_ID_6341"}, "CUX": {"type": "object", "properties": {"CURRENCYDETAILS_01": {"$ref": "#/components/schemas/C504"}, "CURRENCYDETAILS_02": {"$ref": "#/components/schemas/C504"}, "Rateofexchange_03": {"maxLength": 12, "minLength": 1, "type": "string", "format": "EDIFACT_N", "x-openedi-element-id": "5402"}, "Currencymarketexchangecoded_04": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_6341"}], "x-openedi-element-id": "6341"}}, "x-openedi-segment-id": "CUX"}, "Loop_CUX_CUSDEC": {"required": ["CUX"], "type": "object", "properties": {"CUX": {"$ref": "#/components/schemas/CUX"}, "DTM": {"$ref": "#/components/schemas/DTM"}}, "x-openedi-loop-id": "CUX"}, "Loop_MOA_CUSDEC": {"required": ["MOA"], "type": "object", "properties": {"MOA": {"$ref": "#/components/schemas/MOA"}, "CUXLoop": {"$ref": "#/components/schemas/Loop_CUX_CUSDEC"}}, "x-openedi-loop-id": "MOA"}, "UNS": {"required": ["Sectionidentification_01"], "type": "object", "properties": {"Sectionidentification_01": {"maxLength": 1, "minLength": 1, "type": "string", "format": "EDIFACT_A", "x-openedi-element-id": "0081"}}, "x-openedi-segment-id": "UNS"}, "DMS": {"type": "object", "properties": {"Documentmessagenumber_01": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "1004"}, "Documentmessagenamecoded_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1001"}], "x-openedi-element-id": "1001"}, "Totalnumberofitems_03": {"maxLength": 15, "minLength": 1, "type": "string", "format": "EDIFACT_N", "x-openedi-element-id": "7240"}}, "x-openedi-segment-id": "DMS"}, "Loop_NAD_CUSDEC_2": {"required": ["NAD"], "type": "object", "properties": {"NAD": {"$ref": "#/components/schemas/NAD"}, "DOCLoop": {"maxItems": 10, "type": "array", "items": {"$ref": "#/components/schemas/Loop_DOC_CUSDEC"}}}, "x-openedi-loop-id": "NAD"}, "Loop_PCI_CUSDEC_2": {"required": ["PCI"], "type": "object", "properties": {"PCI": {"$ref": "#/components/schemas/PCI"}, "FTX": {"$ref": "#/components/schemas/FTX"}, "RFF": {"$ref": "#/components/schemas/RFF"}}, "x-openedi-loop-id": "PCI"}, "Loop_PAC_CUSDEC_2": {"required": ["PAC"], "type": "object", "properties": {"PAC": {"$ref": "#/components/schemas/PAC"}, "PCILoop": {"maxItems": 999, "type": "array", "items": {"$ref": "#/components/schemas/Loop_PCI_CUSDEC_2"}}}, "x-openedi-loop-id": "PAC"}, "EDIFACT_ID_4279": {"enum": ["1", "10", "11", "12", "13", "14", "15", "16", "17", "18", "2", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "3", "30", "31", "32", "33", "34", "35", "4", "5", "6", "7", "8", "9", "ZZZ"], "type": "string", "format": "EDIFACT_ID_4279"}, "EDIFACT_ID_4277": {"enum": ["1", "2", "3", "4", "5", "6"], "type": "string", "format": "EDIFACT_ID_4277"}, "C110": {"required": ["Termsofpaymentidentification_01"], "type": "object", "properties": {"Termsofpaymentidentification_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_4277"}], "x-openedi-element-id": "4277"}, "Codelistqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}, "Termsofpayment_04": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "4276"}, "Termsofpayment_05": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "4276"}}, "x-openedi-composite-id": "C110"}, "EDIFACT_ID_2475": {"enum": ["1", "11", "12", "13", "14", "2", "21", "22", "23", "24", "25", "26", "27", "28", "29", "3", "31", "32", "33", "4", "41", "42", "43", "44", "45", "46", "47", "48", "5", "52", "53", "54", "55", "56", "57", "6", "60", "61", "62", "63", "64", "65", "66", "67", "68", "69", "7", "70", "71", "72", "73", "74", "75", "76", "77", "78", "79", "8", "80", "81", "9", "ZZZ"], "type": "string", "format": "EDIFACT_ID_2475"}, "EDIFACT_ID_2009": {"enum": ["1", "10", "11", "12", "2", "3", "4", "5", "6", "7", "8", "9"], "type": "string", "format": "EDIFACT_ID_2009"}, "EDIFACT_ID_2151": {"enum": ["3M", "6M", "AA", "AD", "CD", "CW", "D", "DC", "DW", "F", "H", "HM", "M", "MN", "P", "S", "SD", "SI", "W", "WD", "WW", "Y", "ZZZ"], "type": "string", "format": "EDIFACT_ID_2151"}, "C112": {"required": ["Paymenttimereferencecoded_01"], "type": "object", "properties": {"Paymenttimereferencecoded_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_2475"}], "x-openedi-element-id": "2475"}, "Timerelationcoded_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_2009"}], "x-openedi-element-id": "2009"}, "Typeofperiodcoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_2151"}], "x-openedi-element-id": "2151"}, "Numberofperiods_04": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_N", "x-openedi-element-id": "2152"}}, "x-openedi-composite-id": "C112"}, "PAT": {"required": ["Paymenttermstypequalifier_01"], "type": "object", "properties": {"Paymenttermstypequalifier_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_4279"}], "x-openedi-element-id": "4279"}, "PAYMENTTERMS_02": {"$ref": "#/components/schemas/C110"}, "TERMSTIMEINFORMATION_03": {"$ref": "#/components/schemas/C112"}}, "x-openedi-segment-id": "PAT"}, "EDIFACT_ID_5245": {"enum": ["1", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "2", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "3", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "4", "40", "41", "42", "43", "44", "45", "46", "5", "6", "7", "8", "9"], "type": "string", "format": "EDIFACT_ID_5245"}, "EDIFACT_ID_5249": {"enum": ["1", "10", "11", "12", "13", "14", "2", "3", "4", "5", "6", "7", "8", "9"], "type": "string", "format": "EDIFACT_ID_5249"}, "C501": {"required": ["Percentagequalifier_01"], "type": "object", "properties": {"Percentagequalifier_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_5245"}], "x-openedi-element-id": "5245"}, "Percentage_02": {"maxLength": 10, "minLength": 1, "type": "string", "format": "EDIFACT_N", "x-openedi-element-id": "5482"}, "Percentagebasiscoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_5249"}], "x-openedi-element-id": "5249"}, "Codelistqualifier_04": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_05": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}}, "x-openedi-composite-id": "C501"}, "PCD": {"required": ["PERCENTAGEDETAILS_01"], "type": "object", "properties": {"PERCENTAGEDETAILS_01": {"$ref": "#/components/schemas/C501"}}, "x-openedi-segment-id": "PCD"}, "Loop_PAT_CUSDEC": {"required": ["PAT"], "type": "object", "properties": {"PAT": {"$ref": "#/components/schemas/PAT"}, "MOA": {"$ref": "#/components/schemas/MOA"}, "PCD": {"$ref": "#/components/schemas/PCD"}, "FTX": {"$ref": "#/components/schemas/FTX"}}, "x-openedi-loop-id": "PAT"}, "EDIFACT_ID_5463": {"enum": ["A", "B", "C", "D", "E", "F", "G", "H", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y"], "type": "string", "format": "EDIFACT_ID_5463"}, "EDIFACT_ID_5189": {"enum": ["1", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "2", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "3", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "4", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "5", "50", "51", "52", "53", "54", "6", "7", "8", "9"], "type": "string", "format": "EDIFACT_ID_5189"}, "C552": {"type": "object", "properties": {"Allowanceorchargenumber_01": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "1230"}, "Chargeallowancedescriptioncoded_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_5189"}], "x-openedi-element-id": "5189"}}, "x-openedi-composite-id": "C552"}, "EDIFACT_ID_4471": {"enum": ["1", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "2", "20", "21", "22", "23", "24", "3", "4", "5", "6", "7", "8", "9", "ZZZ"], "type": "string", "format": "EDIFACT_ID_4471"}, "EDIFACT_ID_1227": {"enum": ["1", "2", "3", "4", "5", "6", "7", "8", "9"], "type": "string", "format": "EDIFACT_ID_1227"}, "EDIFACT_ID_7161": {"enum": ["AA", "AAA", "AAB", "AAC", "AAD", "AAE", "AAF", "AAG", "AAH", "AAI", "AAJ", "AAK", "AAL", "AAM", "AAN", "AAO", "AAP", "AAQ", "AAR", "AAS", "AAT", "AAU", "AAV", "AAW", "AAX", "AAY", "AAZ", "ABA", "ABB", "ABC", "ABD", "ABE", "ABF", "ABG", "ABH", "ABI", "ABJ", "ABK", "ABL", "ABM", "ABN", "ABO", "ABP", "ABQ", "ABR", "ABS", "ABT", "ABU", "ABV", "ABW", "ABX", "ABY", "ABZ", "ACA", "ACB", "ACC", "ACD", "ACE", "ACF", "ACG", "ACH", "ACI", "ACJ", "ACK", "ACL", "ACM", "ACN", "ACO", "ACP", "ACQ", "ACR", "ACS", "ACT", "ACU", "ACV", "ACW", "ACX", "ACY", "ACZ", "ADA", "ADB", "ADC", "ADD", "ADE", "ADF", "ADG", "ADH", "ADI", "ADJ", "ADK", "ADL", "ADM", "ADN", "ADO", "ADP", "ADQ", "ADR", "ADS", "ADT", "ADU", "ADV", "ADX", "ADY", "AG", "AJ", "AL", "AM", "AU", "CA", "CAA", "CAB", "CAC", "CAD", "CAE", "CAF", "CAG", "CAH", "CAI", "CAJ", "CAK", "CB", "CD", "CG", "CK", "CL", "CO", "CP", "CS", "CT", "CW", "DA", "DAA", "DAB", "DAD", "DAE", "DI", "DL", "DM", "EAA", "EAB", "EG", "EP", "ER", "EX", "FA", "FAA", "FAB", "FAC", "FC", "FG", "FH", "FI", "FN", "FR", "GAA", "HAA", "HD", "HH", "IA", "IAA", "IAB", "ID", "IF", "IN", "IR", "IS", "KO", "L1", "LA", "LAA", "LAB", "LAC", "LF", "LS", "MA", "MAA", "MAB", "MAC", "MAD", "MAE", "MC", "MI", "ML", "NAA", "OA", "OAA", "PA", "PAA", "PAB", "PAC", "PAD", "PAE", "PC", "PD", "PI", "PL", "PN", "PO", "QAA", "QD", "RAA", "RAB", "RAC", "RAD", "RAE", "RAF", "RAG", "RAH", "RE", "RF", "RH", "RO", "RP", "RV", "SA", "SAA", "SAB", "SAC", "SAD", "SAE", "SAF", "SAG", "SAH", "SAI", "SAJ", "SC", "SD", "SF", "SG", "SH", "SM", "ST", "SU", "SZ", "TAA", "TAB", "TAC", "TAD", "TAE", "TD", "TS", "TT", "TV", "TX", "TZ", "UM", "V1", "V2", "VAA", "VAB", "VL", "WH", "XAA", "YY", "ZZZ"], "type": "string", "format": "EDIFACT_ID_7161"}, "C214": {"type": "object", "properties": {"Specialservicescoded_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_7161"}], "x-openedi-element-id": "7161"}, "Codelistqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}, "Specialservice_04": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "7160"}, "Specialservice_05": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "7160"}}, "x-openedi-composite-id": "C214"}, "ALC": {"required": ["Allowanceorchargequalifier_01"], "type": "object", "properties": {"Allowanceorchargequalifier_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_5463"}], "x-openedi-element-id": "5463"}, "ALLOWANCECHARGEINFORMATION_02": {"$ref": "#/components/schemas/C552"}, "Settlementcoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_4471"}], "x-openedi-element-id": "4471"}, "Calculationsequenceindicatorcoded_04": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1227"}], "x-openedi-element-id": "1227"}, "SPECIALSERVICESIDENTIFICATION_05": {"$ref": "#/components/schemas/C214"}}, "x-openedi-segment-id": "ALC"}, "EDIFACT_ID_5419": {"enum": ["1", "2", "3"], "type": "string", "format": "EDIFACT_ID_5419"}, "C128": {"required": ["Rateperunit_02", "Ratetypequalifier_01"], "type": "object", "properties": {"Ratetypequalifier_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_5419"}], "x-openedi-element-id": "5419"}, "Rateperunit_02": {"maxLength": 15, "minLength": 1, "type": "string", "format": "EDIFACT_N", "x-openedi-element-id": "5420"}, "Unitpricebasis_03": {"maxLength": 9, "minLength": 1, "type": "string", "format": "EDIFACT_N", "x-openedi-element-id": "5284"}, "Measureunitqualifier_04": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "6411"}}, "x-openedi-composite-id": "C128"}, "RTE": {"required": ["RATEDETAILS_01"], "type": "object", "properties": {"RATEDETAILS_01": {"$ref": "#/components/schemas/C128"}}, "x-openedi-segment-id": "RTE"}, "EDIFACT_ID_6063": {"enum": ["1", "100", "101", "102", "103", "104", "105", "106", "107", "108", "109", "11", "110", "111", "112", "113", "114", "115", "116", "117", "118", "119", "12", "120", "121", "122", "123", "124", "125", "126", "127", "128", "129", "130", "131", "132", "133", "134", "135", "136", "137", "138", "139", "140", "141", "142", "143", "144", "145", "146", "147", "148", "149", "150", "151", "152", "153", "154", "155", "156", "157", "158", "159", "160", "161", "162", "163", "164", "165", "166", "167", "168", "169", "17", "170", "171", "172", "173", "174", "175", "176", "177", "178", "179", "18", "180", "181", "182", "183", "184", "185", "186", "187", "188", "189", "190", "191", "192", "193", "194", "195", "196", "197", "198", "199", "2", "20", "200", "201", "202", "203", "204", "205", "206", "207", "208", "21", "22", "23", "24", "25", "26", "27", "28", "29", "3", "30", "31", "32", "33", "35", "36", "40", "46", "47", "48", "49", "50", "51", "52", "53", "54", "55", "56", "57", "58", "59", "60", "61", "62", "63", "64", "65", "66", "67", "68", "69", "70", "71", "72", "73", "74", "75", "76", "77", "78", "79", "8", "80", "81", "82", "83", "84", "85", "86", "87", "88", "89", "90", "91", "92", "93", "94", "95", "96", "97", "98", "99"], "type": "string", "format": "EDIFACT_ID_6063"}, "C186": {"required": ["Quantity_02", "Quantityqualifier_01"], "type": "object", "properties": {"Quantityqualifier_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_6063"}], "x-openedi-element-id": "6063"}, "Quantity_02": {"maxLength": 15, "minLength": 1, "type": "string", "format": "EDIFACT_N", "x-openedi-element-id": "6060"}, "Measureunitqualifier_03": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "6411"}}, "x-openedi-composite-id": "C186"}, "QTY": {"required": ["QUANTITYDETAILS_01"], "type": "object", "properties": {"QUANTITYDETAILS_01": {"$ref": "#/components/schemas/C186"}}, "x-openedi-segment-id": "QTY"}, "Loop_ALC_CUSDEC": {"required": ["ALC"], "type": "object", "properties": {"ALC": {"$ref": "#/components/schemas/ALC"}, "RTE": {"$ref": "#/components/schemas/RTE"}, "MOA": {"$ref": "#/components/schemas/MOA"}, "PCD": {"$ref": "#/components/schemas/PCD"}, "QTY": {"$ref": "#/components/schemas/QTY"}, "CUXLoop": {"$ref": "#/components/schemas/Loop_CUX_CUSDEC"}}, "x-openedi-loop-id": "ALC"}, "EDIFACT_ID_1229": {"enum": ["1", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "2", "20", "21", "22", "23", "24", "3", "4", "5", "6", "7", "8", "9"], "type": "string", "format": "EDIFACT_ID_1229"}, "C212": {"type": "object", "properties": {"Itemnumber_01": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "7140"}, "Itemnumbertypecoded_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_7143"}], "x-openedi-element-id": "7143"}, "Codelistqualifier_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_04": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}}, "x-openedi-composite-id": "C212"}, "EDIFACT_ID_5495": {"enum": ["1"], "type": "string", "format": "EDIFACT_ID_5495"}, "C829": {"type": "object", "properties": {"Sublineindicatorcoded_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_5495"}], "x-openedi-element-id": "5495"}, "Lineitemnumber_02": {"maxLength": 6, "minLength": 1, "type": "string", "format": "EDIFACT_N", "x-openedi-element-id": "1082"}}, "x-openedi-composite-id": "C829"}, "EDIFACT_ID_7083": {"enum": ["A", "D", "I"], "type": "string", "format": "EDIFACT_ID_7083"}, "LIN": {"type": "object", "properties": {"Lineitemnumber_01": {"maxLength": 6, "minLength": 1, "type": "string", "format": "EDIFACT_N", "x-openedi-element-id": "1082"}, "Actionrequestnotificationcoded_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1229"}], "x-openedi-element-id": "1229"}, "ITEMNUMBERIDENTIFICATION_03": {"$ref": "#/components/schemas/C212"}, "SUBLINEINFORMATION_04": {"$ref": "#/components/schemas/C829"}, "Configurationlevel_05": {"maxLength": 2, "minLength": 1, "type": "string", "format": "EDIFACT_N", "x-openedi-element-id": "1222"}, "Configurationcoded_06": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_7083"}], "x-openedi-element-id": "7083"}}, "x-openedi-segment-id": "LIN"}, "EDIFACT_ID_4347": {"enum": ["1", "2", "3", "4", "5", "6", "7", "8", "9"], "type": "string", "format": "EDIFACT_ID_4347"}, "PIA": {"required": ["ITEMNUMBERIDENTIFICATION_02", "Productidfunctionqualifier_01"], "type": "object", "properties": {"Productidfunctionqualifier_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_4347"}], "x-openedi-element-id": "4347"}, "ITEMNUMBERIDENTIFICATION_02": {"$ref": "#/components/schemas/C212"}, "ITEMNUMBERIDENTIFICATION_03": {"$ref": "#/components/schemas/C212"}, "ITEMNUMBERIDENTIFICATION_04": {"$ref": "#/components/schemas/C212"}, "ITEMNUMBERIDENTIFICATION_05": {"$ref": "#/components/schemas/C212"}, "ITEMNUMBERIDENTIFICATION_06": {"$ref": "#/components/schemas/C212"}}, "x-openedi-segment-id": "PIA"}, "EDIFACT_ID_5125": {"enum": ["AAA", "AAB", "AAC", "AAD", "AAE", "AAF", "CAL", "INF", "INV"], "type": "string", "format": "EDIFACT_ID_5125"}, "EDIFACT_ID_5375": {"enum": ["AA", "AB", "AC", "AD", "AE", "AI", "AQ", "CA", "CT", "CU", "DI", "EC", "NW", "PC", "PE", "PK", "PL", "PT", "PU", "PV", "PW", "QT", "SR", "TB", "TU", "TW", "WH"], "type": "string", "format": "EDIFACT_ID_5375"}, "EDIFACT_ID_5387": {"enum": ["AAA", "AAB", "AAC", "AAD", "AAE", "AAF", "AAG", "AAH", "AAI", "AAJ", "AAK", "AAL", "AAM", "AAN", "AAO", "AAP", "AAQ", "AAR", "AAS", "AAT", "AAU", "AAV", "AAW", "AAX", "AAY", "AAZ", "ABA", "ABB", "ABC", "ABD", "ABE", "ABF", "AI", "ALT", "AP", "BR", "CAT", "CDV", "CON", "CP", "CU", "CUP", "CUS", "DAP", "DIS", "DPR", "DR", "DSC", "EC", "ES", "EUP", "FCR", "GRP", "INV", "LBL", "MAX", "MIN", "MNR", "MSR", "MXR", "NE", "NQT", "NTP", "NW", "OCR", "OFR", "PAQ", "PBQ", "PPD", "PPR", "PRO", "PRP", "PW", "QTE", "RES", "RTP", "SHD", "SRP", "SW", "TB", "TRF", "TU", "TW", "WH"], "type": "string", "format": "EDIFACT_ID_5387"}, "C509": {"required": ["Pricequalifier_01"], "type": "object", "properties": {"Pricequalifier_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_5125"}], "x-openedi-element-id": "5125"}, "Price_02": {"maxLength": 15, "minLength": 1, "type": "string", "format": "EDIFACT_N", "x-openedi-element-id": "5118"}, "Pricetypecoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_5375"}], "x-openedi-element-id": "5375"}, "Pricetypequalifier_04": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_5387"}], "x-openedi-element-id": "5387"}, "Unitpricebasis_05": {"maxLength": 9, "minLength": 1, "type": "string", "format": "EDIFACT_N", "x-openedi-element-id": "5284"}, "Measureunitqualifier_06": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "6411"}}, "x-openedi-composite-id": "C509"}, "EDIFACT_ID_5213": {"enum": ["A", "I", "S"], "type": "string", "format": "EDIFACT_ID_5213"}, "PRI": {"type": "object", "properties": {"PRICEINFORMATION_01": {"$ref": "#/components/schemas/C509"}, "Sublinepricechangecoded_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_5213"}], "x-openedi-element-id": "5213"}}, "x-openedi-segment-id": "PRI"}, "C279": {"required": ["Quantitydifference_01"], "type": "object", "properties": {"Quantitydifference_01": {"maxLength": 15, "minLength": 1, "type": "string", "format": "EDIFACT_N", "x-openedi-element-id": "6064"}, "Quantityqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_6063"}], "x-openedi-element-id": "6063"}}, "x-openedi-composite-id": "C279"}, "EDIFACT_ID_4221": {"enum": ["AA", "AB", "AC", "AD", "AE", "AF", "AG", "AN", "AS", "BK", "BP", "CA", "CC", "CE", "CI", "CK", "CM", "CN", "CO", "CP", "CS", "IC", "IS", "LS", "NF", "NN", "NS", "OF", "OM", "OP", "OS", "OW", "PA", "PD", "PI", "PK", "PN", "PO", "PP", "PS", "RA", "SL", "SP", "SS", "TW", "UR", "ZZZ"], "type": "string", "format": "EDIFACT_ID_4221"}, "EDIFACT_ID_4295": {"enum": ["AA", "AB", "AC", "AD", "AE", "AF", "AG", "AH", "AI", "AJ", "AK", "AL", "AM", "AN", "AO", "AP", "AQ", "AR", "AS", "AT", "AU", "AV", "AW", "BD", "BQ", "DC", "EV", "GU", "GW", "LD", "MC", "PC", "PD", "PQ", "PS", "PW", "PZ", "QO", "QP", "QT", "SC", "UM", "UP", "WD", "WO", "ZZZ"], "type": "string", "format": "EDIFACT_ID_4295"}, "C960": {"type": "object", "properties": {"Changereasoncoded_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_4295"}], "x-openedi-element-id": "4295"}, "Codelistqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}, "Changereason_04": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "4294"}}, "x-openedi-composite-id": "C960"}, "QVR": {"type": "object", "properties": {"QUANTITYDIFFERENCEINFORMATION_01": {"$ref": "#/components/schemas/C279"}, "Discrepancycoded_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_4221"}], "x-openedi-element-id": "4221"}, "REASONFORCHANGE_03": {"$ref": "#/components/schemas/C960"}}, "x-openedi-segment-id": "QVR"}, "EDIFACT_ID_7297": {"enum": ["1", "2", "3", "4", "5", "6", "7", "8", "9"], "type": "string", "format": "EDIFACT_ID_7297"}, "EDIFACT_ID_7405": {"enum": ["AA", "AB", "AC", "AD", "AE", "AF", "AG", "AH", "AI", "AJ", "AK", "AL", "AM", "AN", "AO", "AP", "AQ", "AR", "AS", "AT", "AU", "AV", "AW", "AX", "AY", "AZ", "BA", "BB", "BC", "BD", "BE", "BF", "BG", "BH", "BI", "BJ", "BK", "BL", "BN", "BX", "CN", "EE", "EM", "IL", "ML", "PN", "SC", "VV"], "type": "string", "format": "EDIFACT_ID_7405"}, "C206": {"required": ["Identitynumber_01"], "type": "object", "properties": {"Identitynumber_01": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "7402"}, "Identitynumberqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_7405"}], "x-openedi-element-id": "7405"}, "Statuscoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_4405"}], "x-openedi-element-id": "4405"}}, "x-openedi-composite-id": "C206"}, "GIR": {"required": ["IDENTIFICATIONNUMBER_02", "Setidentificationqualifier_01"], "type": "object", "properties": {"Setidentificationqualifier_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_7297"}], "x-openedi-element-id": "7297"}, "IDENTIFICATIONNUMBER_02": {"$ref": "#/components/schemas/C206"}, "IDENTIFICATIONNUMBER_03": {"$ref": "#/components/schemas/C206"}, "IDENTIFICATIONNUMBER_04": {"$ref": "#/components/schemas/C206"}, "IDENTIFICATIONNUMBER_05": {"$ref": "#/components/schemas/C206"}, "IDENTIFICATIONNUMBER_06": {"$ref": "#/components/schemas/C206"}}, "x-openedi-segment-id": "GIR"}, "Loop_DOC_CUSDEC_2": {"required": ["DOC"], "type": "object", "properties": {"DOC": {"$ref": "#/components/schemas/DOC"}, "DTM": {"maxItems": 2, "type": "array", "items": {"$ref": "#/components/schemas/DTM"}}}, "x-openedi-loop-id": "DOC"}, "Loop_TOD_CUSDEC_2": {"required": ["TOD"], "type": "object", "properties": {"TOD": {"$ref": "#/components/schemas/TOD"}, "LOC": {"maxItems": 2, "type": "array", "items": {"$ref": "#/components/schemas/LOC"}}}, "x-openedi-loop-id": "TOD"}, "EDIFACT_ID_7081": {"enum": ["1", "10", "100", "101", "102", "103", "11", "12", "13", "14", "15", "17", "18", "19", "2", "21", "22", "23", "24", "25", "26", "28", "3", "30", "32", "35", "38", "4", "43", "5", "54", "56", "58", "59", "61", "62", "63", "64", "65", "66", "67", "68", "69", "70", "71", "72", "73", "74", "75", "76", "77", "78", "79", "8", "80", "81", "82", "83", "84", "85", "86", "87", "88", "89", "9", "90", "91", "92", "93", "94", "95", "96", "97", "98"], "type": "string", "format": "EDIFACT_ID_7081"}, "C273": {"type": "object", "properties": {"Itemdescriptionidentification_01": {"maxLength": 17, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "7009"}, "Codelistqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}, "Itemdescription_04": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "7008"}, "Itemdescription_05": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "7008"}, "Languagecoded_06": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3453"}}, "x-openedi-composite-id": "C273"}, "IMD": {"type": "object", "properties": {"Itemdescriptiontypecoded_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_7077"}], "x-openedi-element-id": "7077"}, "Itemcharacteristiccoded_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_7081"}], "x-openedi-element-id": "7081"}, "ITEMDESCRIPTION_03": {"$ref": "#/components/schemas/C273"}, "Surfacelayerindicatorcoded_04": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_7383"}], "x-openedi-element-id": "7383"}}, "x-openedi-segment-id": "IMD"}, "Loop_IMD_CUSDEC": {"required": ["IMD"], "type": "object", "properties": {"IMD": {"$ref": "#/components/schemas/IMD"}, "FTX": {"$ref": "#/components/schemas/FTX"}}, "x-openedi-loop-id": "IMD"}, "Loop_LIN_CUSDEC": {"required": ["LIN"], "type": "object", "properties": {"LIN": {"$ref": "#/components/schemas/LIN"}, "PIA": {"$ref": "#/components/schemas/PIA"}, "QTY": {"$ref": "#/components/schemas/QTY"}, "PRI": {"maxItems": 2, "type": "array", "items": {"$ref": "#/components/schemas/PRI"}}, "PCD": {"$ref": "#/components/schemas/PCD"}, "MEA": {"maxItems": 5, "type": "array", "items": {"$ref": "#/components/schemas/MEA"}}, "QVR": {"maxItems": 5, "type": "array", "items": {"$ref": "#/components/schemas/QVR"}}, "MOA": {"maxItems": 15, "type": "array", "items": {"$ref": "#/components/schemas/MOA"}}, "NAD": {"maxItems": 10, "type": "array", "items": {"$ref": "#/components/schemas/NAD"}}, "GIR": {"maxItems": 9999, "type": "array", "items": {"$ref": "#/components/schemas/GIR"}}, "DOCLoop": {"maxItems": 10, "type": "array", "items": {"$ref": "#/components/schemas/Loop_DOC_CUSDEC_2"}}, "ALCLoop": {"maxItems": 10, "type": "array", "items": {"$ref": "#/components/schemas/Loop_ALC_CUSDEC"}}, "TODLoop": {"$ref": "#/components/schemas/Loop_TOD_CUSDEC_2"}, "PATLoop": {"maxItems": 5, "type": "array", "items": {"$ref": "#/components/schemas/Loop_PAT_CUSDEC"}}, "IMDLoop": {"maxItems": 5, "type": "array", "items": {"$ref": "#/components/schemas/Loop_IMD_CUSDEC"}}, "PACLoop": {"maxItems": 10, "type": "array", "items": {"$ref": "#/components/schemas/Loop_PAC_CUSDEC_2"}}}, "x-openedi-loop-id": "LIN"}, "Loop_DMS_CUSDEC": {"required": ["DMS"], "type": "object", "properties": {"DMS": {"$ref": "#/components/schemas/DMS"}, "DTM": {"$ref": "#/components/schemas/DTM"}, "MEA": {"maxItems": 5, "type": "array", "items": {"$ref": "#/components/schemas/MEA"}}, "MOALoop": {"maxItems": 25, "type": "array", "items": {"$ref": "#/components/schemas/Loop_MOA_CUSDEC"}}, "TODLoop": {"$ref": "#/components/schemas/Loop_TOD_CUSDEC"}, "NADLoop": {"maxItems": 10, "type": "array", "items": {"$ref": "#/components/schemas/Loop_NAD_CUSDEC_2"}}, "PACLoop": {"maxItems": 10, "type": "array", "items": {"$ref": "#/components/schemas/Loop_PAC_CUSDEC_2"}}, "PATLoop": {"maxItems": 5, "type": "array", "items": {"$ref": "#/components/schemas/Loop_PAT_CUSDEC"}}, "ALCLoop": {"maxItems": 10, "type": "array", "items": {"$ref": "#/components/schemas/Loop_ALC_CUSDEC"}}, "LINLoop": {"maxItems": 9999, "type": "array", "items": {"$ref": "#/components/schemas/Loop_LIN_CUSDEC"}}}, "x-openedi-loop-id": "DMS"}, "C208": {"required": ["Identitynumber_01"], "type": "object", "properties": {"Identitynumber_01": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "7402"}, "Identitynumber_02": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "7402"}}, "x-openedi-composite-id": "C208"}, "GIN": {"required": ["Identitynumberqualifier_01", "IDENTITYNUMBERRANGE_02"], "type": "object", "properties": {"Identitynumberqualifier_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_7405"}], "x-openedi-element-id": "7405"}, "IDENTITYNUMBERRANGE_02": {"$ref": "#/components/schemas/C208"}, "IDENTITYNUMBERRANGE_03": {"$ref": "#/components/schemas/C208"}, "IDENTITYNUMBERRANGE_04": {"$ref": "#/components/schemas/C208"}, "IDENTITYNUMBERRANGE_05": {"$ref": "#/components/schemas/C208"}, "IDENTITYNUMBERRANGE_06": {"$ref": "#/components/schemas/C208"}}, "x-openedi-segment-id": "GIN"}, "Loop_IMD_CUSDEC_2": {"required": ["IMD"], "type": "object", "properties": {"IMD": {"$ref": "#/components/schemas/IMD"}, "FTX": {"maxItems": 5, "type": "array", "items": {"$ref": "#/components/schemas/FTX"}}}, "x-openedi-loop-id": "IMD"}, "Loop_RFF_CUSDEC_2": {"required": ["RFF"], "type": "object", "properties": {"RFF": {"$ref": "#/components/schemas/RFF"}, "DTM": {"$ref": "#/components/schemas/DTM"}, "GIN": {"maxItems": 99, "type": "array", "items": {"$ref": "#/components/schemas/GIN"}}, "MOA": {"$ref": "#/components/schemas/MOA"}, "IMDLoop": {"$ref": "#/components/schemas/Loop_IMD_CUSDEC_2"}}, "x-openedi-loop-id": "RFF"}, "Loop_DOC_CUSDEC_3": {"required": ["DOC"], "type": "object", "properties": {"DOC": {"$ref": "#/components/schemas/DOC"}, "DTM": {"maxItems": 2, "type": "array", "items": {"$ref": "#/components/schemas/DTM"}}, "LOC": {"$ref": "#/components/schemas/LOC"}, "NAD": {"maxItems": 5, "type": "array", "items": {"$ref": "#/components/schemas/NAD"}}}, "x-openedi-loop-id": "DOC"}, "EDIFACT_ID_7085": {"enum": ["1", "10", "11", "12", "13", "14", "15", "16", "2", "3", "4", "5", "6", "7", "8", "9"], "type": "string", "format": "EDIFACT_ID_7085"}, "C703": {"required": ["Natureofcargocoded_01"], "type": "object", "properties": {"Natureofcargocoded_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_7085"}], "x-openedi-element-id": "7085"}, "Codelistqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}}, "x-openedi-composite-id": "C703"}, "GDS": {"type": "object", "properties": {"NATUREOFCARGO_01": {"$ref": "#/components/schemas/C703"}}, "x-openedi-segment-id": "GDS"}, "Loop_GDS_CUSDEC": {"required": ["GDS"], "type": "object", "properties": {"GDS": {"$ref": "#/components/schemas/GDS"}, "FTX": {"$ref": "#/components/schemas/FTX"}}, "x-openedi-loop-id": "GDS"}, "Loop_GIS_CUSDEC": {"required": ["GIS"], "type": "object", "properties": {"GIS": {"$ref": "#/components/schemas/GIS"}, "PCD": {"$ref": "#/components/schemas/PCD"}, "DTM": {"$ref": "#/components/schemas/DTM"}, "RFF": {"$ref": "#/components/schemas/RFF"}}, "x-openedi-loop-id": "GIS"}, "EDIFACT_ID_5283": {"enum": ["1", "2", "3", "4", "5", "6", "7", "9"], "type": "string", "format": "EDIFACT_ID_5283"}, "EDIFACT_ID_5153": {"enum": ["AAA", "AAB", "AAC", "AAD", "ADD", "BOL", "CAP", "CAR", "COC", "CST", "CUD", "CVD", "ENV", "EXC", "EXP", "FET", "FRE", "GCN", "GST", "ILL", "IMP", "IND", "LAC", "LCN", "LDP", "LOC", "LST", "MCA", "MCD", "OTH", "PDB", "PDC", "PRF", "SCN", "SSS", "STT", "SUP", "SUR", "SWT", "TAC", "TOT", "TOX", "TTA", "VAD", "VAT"], "type": "string", "format": "EDIFACT_ID_5153"}, "C241": {"type": "object", "properties": {"Dutytaxfeetypecoded_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_5153"}], "x-openedi-element-id": "5153"}, "Codelistqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}, "Dutytaxfeetype_04": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "5152"}}, "x-openedi-composite-id": "C241"}, "C533": {"required": ["Dutytaxfeeaccountidentification_01"], "type": "object", "properties": {"Dutytaxfeeaccountidentification_01": {"maxLength": 6, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "5289"}, "Codelistqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}}, "x-openedi-composite-id": "C533"}, "EDIFACT_ID_5273": {"enum": ["1", "2", "3"], "type": "string", "format": "EDIFACT_ID_5273"}, "C243": {"type": "object", "properties": {"Dutytaxfeerateidentification_01": {"maxLength": 7, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "5279"}, "Codelistqualifier_02": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_03": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}, "Dutytaxfeerate_04": {"maxLength": 17, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "5278"}, "Dutytaxfeeratebasisidentification_05": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_5273"}], "x-openedi-element-id": "5273"}, "Codelistqualifier_06": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_1131"}], "x-openedi-element-id": "1131"}, "Codelistresponsibleagencycoded_07": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_3055"}], "x-openedi-element-id": "3055"}}, "x-openedi-composite-id": "C243"}, "EDIFACT_ID_5305": {"enum": ["A", "B", "C", "E", "G", "H", "O", "S", "Z"], "type": "string", "format": "EDIFACT_ID_5305"}, "TAX": {"required": ["Dutytaxfeefunctionqualifier_01"], "type": "object", "properties": {"Dutytaxfeefunctionqualifier_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_5283"}], "x-openedi-element-id": "5283"}, "DUTYTAXFEETYPE_02": {"$ref": "#/components/schemas/C241"}, "DUTYTAXFEEACCOUNTDETAIL_03": {"$ref": "#/components/schemas/C533"}, "Dutytaxfeeassessmentbasis_04": {"maxLength": 15, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "5286"}, "DUTYTAXFEEDETAIL_05": {"$ref": "#/components/schemas/C243"}, "Dutytaxfeecategorycoded_06": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_5305"}], "x-openedi-element-id": "5305"}, "Partytaxidentificationnumber_07": {"maxLength": 20, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "3446"}}, "x-openedi-segment-id": "TAX"}, "Loop_TAX_CUSDEC": {"required": ["TAX"], "type": "object", "properties": {"TAX": {"$ref": "#/components/schemas/TAX"}, "MOA": {"maxItems": 2, "type": "array", "items": {"$ref": "#/components/schemas/MOA"}}, "GIS": {"$ref": "#/components/schemas/GIS"}}, "x-openedi-loop-id": "TAX"}, "Loop_RFF_CUSDEC_3": {"required": ["RFF"], "type": "object", "properties": {"RFF": {"$ref": "#/components/schemas/RFF"}, "DTM": {"$ref": "#/components/schemas/DTM"}}, "x-openedi-loop-id": "RFF"}, "Loop_QVR_CUSDEC": {"required": ["QVR"], "type": "object", "properties": {"QVR": {"$ref": "#/components/schemas/QVR"}, "QTY": {"$ref": "#/components/schemas/QTY"}, "RFFLoop": {"$ref": "#/components/schemas/Loop_RFF_CUSDEC_3"}}, "x-openedi-loop-id": "QVR"}, "Loop_GIR_CUSDEC": {"required": ["GIR"], "type": "object", "properties": {"GIR": {"$ref": "#/components/schemas/GIR"}, "GIS": {"maxItems": 10, "type": "array", "items": {"$ref": "#/components/schemas/GIS"}}, "NAD": {"$ref": "#/components/schemas/NAD"}, "MEA": {"maxItems": 5, "type": "array", "items": {"$ref": "#/components/schemas/MEA"}}, "MOALoop": {"maxItems": 10, "type": "array", "items": {"$ref": "#/components/schemas/Loop_MOA_CUSDEC"}}, "TAXLoop": {"maxItems": 10, "type": "array", "items": {"$ref": "#/components/schemas/Loop_TAX_CUSDEC"}}, "DOCLoop": {"maxItems": 5, "type": "array", "items": {"$ref": "#/components/schemas/Loop_DOC_CUSDEC"}}}, "x-openedi-loop-id": "GIR"}, "Loop_CST_CUSDEC": {"required": ["CST"], "type": "object", "properties": {"CST": {"$ref": "#/components/schemas/CST"}, "FTX": {"maxItems": 9, "type": "array", "items": {"$ref": "#/components/schemas/FTX"}}, "LOC": {"maxItems": 25, "type": "array", "items": {"$ref": "#/components/schemas/LOC"}}, "DTM": {"maxItems": 5, "type": "array", "items": {"$ref": "#/components/schemas/DTM"}}, "MEA": {"maxItems": 20, "type": "array", "items": {"$ref": "#/components/schemas/MEA"}}, "NAD": {"maxItems": 5, "type": "array", "items": {"$ref": "#/components/schemas/NAD"}}, "TDT": {"maxItems": 9, "type": "array", "items": {"$ref": "#/components/schemas/TDT"}}, "PACLoop": {"maxItems": 10, "type": "array", "items": {"$ref": "#/components/schemas/Loop_PAC_CUSDEC"}}, "MOALoop": {"maxItems": 25, "type": "array", "items": {"$ref": "#/components/schemas/Loop_MOA_CUSDEC"}}, "RFFLoop": {"maxItems": 999, "type": "array", "items": {"$ref": "#/components/schemas/Loop_RFF_CUSDEC_2"}}, "DOCLoop": {"maxItems": 25, "type": "array", "items": {"$ref": "#/components/schemas/Loop_DOC_CUSDEC_3"}}, "TODLoop": {"$ref": "#/components/schemas/Loop_TOD_CUSDEC"}, "GDSLoop": {"$ref": "#/components/schemas/Loop_GDS_CUSDEC"}, "GISLoop": {"maxItems": 25, "type": "array", "items": {"$ref": "#/components/schemas/Loop_GIS_CUSDEC"}}, "TAXLoop": {"maxItems": 10, "type": "array", "items": {"$ref": "#/components/schemas/Loop_TAX_CUSDEC"}}, "QVRLoop": {"$ref": "#/components/schemas/Loop_QVR_CUSDEC"}, "GIRLoop": {"maxItems": 50, "type": "array", "items": {"$ref": "#/components/schemas/Loop_GIR_CUSDEC"}}}, "x-openedi-loop-id": "CST"}, "EDIFACT_ID_6069": {"enum": ["1", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "2", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "3", "30", "31", "4", "5", "6", "7", "8", "9"], "type": "string", "format": "EDIFACT_ID_6069"}, "C270": {"required": ["Controlqualifier_01", "Controlvalue_02"], "type": "object", "properties": {"Controlqualifier_01": {"type": "string", "allOf": [{"$ref": "#/components/schemas/EDIFACT_ID_6069"}], "x-openedi-element-id": "6069"}, "Controlvalue_02": {"maxLength": 18, "minLength": 1, "type": "string", "format": "EDIFACT_N", "x-openedi-element-id": "6066"}, "Measureunitqualifier_03": {"maxLength": 3, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "6411"}}, "x-openedi-composite-id": "C270"}, "CNT": {"required": ["CONTROL_01"], "type": "object", "properties": {"CONTROL_01": {"$ref": "#/components/schemas/C270"}}, "x-openedi-segment-id": "CNT"}, "AUT": {"required": ["Validationresult_01"], "type": "object", "properties": {"Validationresult_01": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "9280"}, "Validationkeyidentification_02": {"maxLength": 35, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "9282"}}, "x-openedi-segment-id": "AUT"}, "Loop_AUT_CUSDEC": {"required": ["AUT"], "type": "object", "properties": {"AUT": {"$ref": "#/components/schemas/AUT"}, "DTM": {"$ref": "#/components/schemas/DTM"}}, "x-openedi-loop-id": "AUT"}, "UNT": {"required": ["MessageReferenceNumber_02", "NumberofSegmentsinaMessage_01"], "type": "object", "properties": {"NumberofSegmentsinaMessage_01": {"maxLength": 10, "minLength": 1, "type": "string", "format": "EDIFACT_N", "x-openedi-element-id": "0074"}, "MessageReferenceNumber_02": {"maxLength": 14, "minLength": 1, "type": "string", "format": "EDIFACT_AN", "x-openedi-element-id": "0062"}}, "x-openedi-segment-id": "UNT"}, "TSCUSDEC": {"required": ["BGM", "UNS", "UNS2"], "type": "object", "properties": {"Model": {"type": "string"}, "UNH": {"$ref": "#/components/schemas/UNH"}, "BGM": {"$ref": "#/components/schemas/BGM"}, "CST": {"$ref": "#/components/schemas/CST"}, "LOC": {"maxItems": 99, "type": "array", "items": {"$ref": "#/components/schemas/LOC"}}, "DTM": {"maxItems": 15, "type": "array", "items": {"$ref": "#/components/schemas/DTM"}}, "GIS": {"maxItems": 25, "type": "array", "items": {"$ref": "#/components/schemas/GIS"}}, "FII": {"$ref": "#/components/schemas/FII"}, "MEA": {"maxItems": 5, "type": "array", "items": {"$ref": "#/components/schemas/MEA"}}, "EQD": {"maxItems": 999, "type": "array", "items": {"$ref": "#/components/schemas/EQD"}}, "SEL": {"maxItems": 999, "type": "array", "items": {"$ref": "#/components/schemas/SEL"}}, "FTX": {"maxItems": 99, "type": "array", "items": {"$ref": "#/components/schemas/FTX"}}, "RFFLoop": {"maxItems": 99, "type": "array", "items": {"$ref": "#/components/schemas/Loop_RFF_CUSDEC"}}, "TDTLoop": {"maxItems": 5, "type": "array", "items": {"$ref": "#/components/schemas/Loop_TDT_CUSDEC"}}, "DOCLoop": {"maxItems": 10, "type": "array", "items": {"$ref": "#/components/schemas/Loop_DOC_CUSDEC"}}, "NADLoop": {"maxItems": 10, "type": "array", "items": {"$ref": "#/components/schemas/Loop_NAD_CUSDEC"}}, "TODLoop": {"$ref": "#/components/schemas/Loop_TOD_CUSDEC"}, "MOALoop": {"maxItems": 25, "type": "array", "items": {"$ref": "#/components/schemas/Loop_MOA_CUSDEC"}}, "UNS": {"$ref": "#/components/schemas/UNS"}, "DMSLoop": {"maxItems": 999, "type": "array", "items": {"$ref": "#/components/schemas/Loop_DMS_CUSDEC"}}, "CSTLoop": {"maxItems": 9999, "type": "array", "items": {"$ref": "#/components/schemas/Loop_CST_CUSDEC"}}, "UNS2": {"$ref": "#/components/schemas/UNS"}, "CNT": {"maxItems": 5, "type": "array", "items": {"$ref": "#/components/schemas/CNT"}}, "TAXLoop": {"maxItems": 50, "type": "array", "items": {"$ref": "#/components/schemas/Loop_TAX_CUSDEC"}}, "AUTLoop": {"$ref": "#/components/schemas/Loop_AUT_CUSDEC"}, "UNT": {"$ref": "#/components/schemas/UNT"}}, "x-openedi-message-id": "CUSDEC", "x-openedi-message-standard": "EDIFACT"}}}}