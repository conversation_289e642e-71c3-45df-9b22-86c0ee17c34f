{"messageHeader": {"messageType": "CUSDEC", "messageVersion": "30", "messageRelease": "30", "controllingAgency": "SARS", "messageReference": "SARS001", "messageFunction": "9"}, "beginningOfMessage": {"documentNameCode": "952", "documentNumber": "SARS20240101001", "messageFunction": "9", "responseType": "AB"}, "declarationType": "SAD500", "customsOfficeCode": "JHB", "declarantReference": "DECL123456", "transportMode": "3", "dateTimePeriod": [{"qualifier": "137", "dateTime": "20240101", "formatQualifier": "102"}, {"qualifier": "182", "dateTime": "20240101", "formatQualifier": "102"}], "locations": [{"qualifier": "5", "locationIdentification": {"locationId": "DEHAM", "codeListQualifier": "139", "codeListResponsibleAgency": "6"}}, {"qualifier": "8", "locationIdentification": {"locationId": "USLAX", "codeListQualifier": "139", "codeListResponsibleAgency": "6"}}], "references": [{"qualifier": "ABT", "referenceNumber": "MRN123456789", "documentLineIdentifier": "1"}], "customsStatusOfGoods": [{"goodsItemNumber": "1", "customsIdentityCodes": [{"customsCodeId": "4000", "codeListQualifier": "23", "codeListResponsibleAgency": "ZZZ"}]}], "nameAndAddress": [{"partyQualifier": "EX", "partyIdentification": {"partyId": "EXPORTER123", "codeListQualifier": "ZZZ"}, "nameAndAddress": {"name": "Test Exporter Ltd", "street": "123 Export Street", "city": "Cape Town", "postcode": "8001", "countryCode": "ZA"}, "tinNumber": "1234567890", "vatNumber": "4123456789"}, {"partyQualifier": "IM", "partyIdentification": {"partyId": "IMPORTER456", "codeListQualifier": "ZZZ"}, "nameAndAddress": {"name": "Test Importer Inc", "street": "456 Import Avenue", "city": "Johannesburg", "postcode": "2000", "countryCode": "ZA"}, "tinNumber": "9876543210", "vatNumber": "4987654321"}, {"partyQualifier": "DT", "partyIdentification": {"partyId": "DECLARANT789", "codeListQualifier": "ZZZ"}, "nameAndAddress": {"name": "SARS Customs Agent Ltd", "street": "789 Customs Road", "city": "Johannesburg", "postcode": "2000", "countryCode": "ZA"}, "tinNumber": "5555555555", "customsCode": "CUST12345"}], "goodsItems": [{"lineItem": {"lineItemNumber": "1", "itemNumberIdentification": {"itemNumber": "8471300000", "itemNumberType": "HS"}}, "itemDescription": {"itemDescriptionType": "F", "itemDescription": "Laptop computers"}, "measurements": [{"measurementQualifier": "AAD", "measurementDetails": {"measurementValue": "100.5", "measurementUnitCode": "KGM"}}, {"measurementQualifier": "AAE", "measurementDetails": {"measurementValue": "95.0", "measurementUnitCode": "KGM"}}], "quantities": [{"quantityQualifier": "12", "quantityDetails": {"quantity": "10", "measurementUnitCode": "PCE"}}], "monetaryAmounts": [{"monetaryAmountQualifier": "203", "monetaryAmountDetails": {"currencyQualifier": "4", "monetaryAmount": "5000.00", "currencyCode": "USD"}}], "placeLocationIdentification": [{"placeLocationQualifier": "35", "locationIdentification": {"locationId": "ZA", "codeListQualifier": "162", "codeListResponsibleAgency": "5"}}], "customsValue": 4500.0, "dutyValue": 450.0, "vatValue": 765.0, "tariffCode": "8471300000", "packageIdentification": [{"packagingLevelCode": "1", "packagingDetails": {"packagingTermsAndConditionsCoded": "BX", "packageTypeDescription": "Box"}, "packageIdentification": {"packagingUnitQuantity": "10"}}]}], "controlTotal": [{"controlQualifier": "2", "controlValue": "1"}], "authenticationResult": {"validationResult": "1", "validationKeyName": "SARS_CUSDEC_VALIDATION"}, "parties": {"exporter": {"partyIdQualifier": "ZZZ", "partyId": "EXPORTER123", "name": "Test Exporter Ltd", "street": "123 Export Street", "city": "Cape Town", "postcode": "8001", "countryCode": "ZA", "tinNumber": "1234567890", "vatNumber": "4123456789"}, "importer": {"partyIdQualifier": "ZZZ", "partyId": "IMPORTER456", "name": "Test Importer Inc", "street": "456 Import Avenue", "city": "Johannesburg", "postcode": "2000", "countryCode": "ZA", "tinNumber": "9876543210", "vatNumber": "4987654321"}, "declarant": {"partyIdQualifier": "ZZZ", "partyId": "DECLARANT789", "name": "SARS Customs Agent Ltd", "street": "789 Customs Road", "city": "Johannesburg", "postcode": "2000", "countryCode": "ZA", "tinNumber": "5555555555", "customsCode": "CUST12345"}}}