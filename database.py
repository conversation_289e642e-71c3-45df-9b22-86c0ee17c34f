"""
Database module for the CUSDEC microservice.
"""

import os
from sqlalchemy import create_engine, Column, Integer, String, Float, ForeignKey, DateTime, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
import datetime
import json

# Create data directory if it doesn't exist
data_dir = os.path.join(os.getcwd(), "data")
os.makedirs(data_dir, exist_ok=True)

# Create database engine
DATABASE_URL = os.getenv("DATABASE_URL", f"sqlite:///{os.path.join(data_dir, 'cusdec.db')}")
engine = create_engine(DATABASE_URL)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create base class for models
Base = declarative_base()

# Define database models
class CUSDECRecord(Base):
    """Database model for CUSDEC records."""

    __tablename__ = "cusdec_records"

    id = Column(Integer, primary_key=True, index=True)
    document_number = Column(String, unique=True, index=True)
    document_name_code = Column(String, default="952")
    message_function = Column(String, default="9")
    document_date = Column(String)
    declaration_date = Column(String)
    place_of_departure = Column(String)
    place_of_destination = Column(String)
    control_result = Column(String, nullable=True)
    authentication_type = Column(String, nullable=True)
    authentication_place = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    # SARS-specific fields
    declaration_type = Column(String, nullable=True)
    customs_office_code = Column(String, nullable=True)
    declarant_reference = Column(String, nullable=True)
    transport_mode = Column(String, nullable=True)
    previous_customs_regime = Column(String, nullable=True)
    value_added_tax_number = Column(String, nullable=True)
    customs_procedure = Column(String, nullable=True)
    previous_procedure = Column(String, nullable=True)
    exchange_rate = Column(Float, nullable=True)
    exchange_rate_date = Column(String, nullable=True)
    total_gross_weight = Column(Float, nullable=True)
    total_net_weight = Column(Float, nullable=True)
    total_packages = Column(Integer, nullable=True)
    total_invoice_amount = Column(Float, nullable=True)
    total_customs_value = Column(Float, nullable=True)
    total_duty_amount = Column(Float, nullable=True)
    total_vat_amount = Column(Float, nullable=True)

    # Store complex data as JSON
    references = Column(JSON, default=list)
    parties = Column(JSON)
    goods_items = Column(JSON)

    # Store generated EDIFACT message
    edifact_message = Column(String, nullable=True)
    message_reference = Column(String, nullable=True)

    def to_dict(self):
        """Convert record to dictionary."""
        return {
            "id": self.id,
            "documentNumber": self.document_number,
            "documentNameCode": self.document_name_code,
            "messageFunction": self.message_function,
            "documentDate": self.document_date,
            "declarationDate": self.declaration_date,
            "placeOfDeparture": self.place_of_departure,
            "placeOfDestination": self.place_of_destination,
            "controlResult": self.control_result,
            "authenticationType": self.authentication_type,
            "authenticationPlace": self.authentication_place,
            # SARS-specific fields
            "declarationType": self.declaration_type,
            "customsOfficeCode": self.customs_office_code,
            "declarantReference": self.declarant_reference,
            "transportMode": self.transport_mode,
            "previousCustomsRegime": self.previous_customs_regime,
            "valueAddedTaxNumber": self.value_added_tax_number,
            "customsProcedure": self.customs_procedure,
            "previousProcedure": self.previous_procedure,
            "exchangeRate": self.exchange_rate,
            "exchangeRateDate": self.exchange_rate_date,
            "totalGrossWeight": self.total_gross_weight,
            "totalNetWeight": self.total_net_weight,
            "totalPackages": self.total_packages,
            "totalInvoiceAmount": self.total_invoice_amount,
            "totalCustomsValue": self.total_customs_value,
            "totalDutyAmount": self.total_duty_amount,
            "totalVatAmount": self.total_vat_amount,
            # Complex data
            "references": self.references,
            "parties": self.parties,
            "goodsItems": self.goods_items,
            "edifactMessage": self.edifact_message,
            "messageReference": self.message_reference,
            "createdAt": self.created_at.isoformat() if self.created_at else None,
            "updatedAt": self.updated_at.isoformat() if self.updated_at else None
        }

# Create tables
def create_tables():
    """Create database tables."""
    Base.metadata.create_all(bind=engine)

# Get database session
def get_db():
    """Get database session."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
