services:
  cusdec-service:
    container_name: cusdec-service
    build: .
    ports:
      - "8005:8000"
    env_file:
      - .env
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=${PROD_DATABASE_URL}
      - LOG_FILE=/app/logs/cusdec_service.log
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
