"""
Core EDIFACT components for message generation.
"""

import logging
import datetime
import uuid
from typing import List, Union, Any
from config import Config

logger = logging.getLogger("cusdec_service")


class EDIFACTElement:
    """Base class for EDIFACT data elements."""

    def __init__(self, value: Any = None):
        self.value = value

    def format(self) -> str:
        """Format the element value according to EDIFACT rules."""
        if self.value is None:
            return ""

        # Convert to string and escape special characters
        value_str = str(self.value)
        for char, escaped in [
            (Config.RELEASE_CHARACTER, Config.RELEASE_CHARACTER + Config.RELEASE_CHARACTER),
            (Config.SEGMENT_TERMINATOR, Config.RELEASE_CHARACTER + Config.SEGMENT_TERMINATOR),
            (Config.DATA_ELEMENT_SEPARATOR, Config.RELEASE_CHARACTER + Config.DATA_ELEMENT_SEPARATOR),
            (Config.COMPONENT_DATA_SEPARATOR, Config.RELEASE_CHARACTER + Config.COMPONENT_DATA_SEPARATOR)
        ]:
            value_str = value_str.replace(char, escaped)

        return value_str


class EDIFACTSegment:
    """Base class for EDIFACT segments."""

    def __init__(self, tag: str, elements: List[Union[EDIFACTElement, List[EDIFACTElement]]] = None):
        self.tag = tag
        self.elements = elements or []

    def format(self) -> str:
        """Format the segment according to EDIFACT rules."""
        try:
            result = self.tag

            for element in self.elements:
                result += Config.DATA_ELEMENT_SEPARATOR

                if isinstance(element, list):
                    # Handle composite elements
                    component_values = []
                    for component in element:
                        if component is None:
                            logger.warning(f"None component found in segment {self.tag}")
                            component_values.append("")
                        else:
                            component_values.append(component.format())
                    result += Config.COMPONENT_DATA_SEPARATOR.join(component_values)
                else:
                    # Simple element
                    if element is None:
                        logger.warning(f"None element found in segment {self.tag}")
                        result += ""
                    else:
                        result += element.format()

            result += Config.SEGMENT_TERMINATOR
            return result
        except Exception as e:
            logger.error(f"Error formatting segment {self.tag}: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise


class EDIFACTMessage:
    """Class representing a complete EDIFACT message."""

    def __init__(self, message_type: str, segments: List[EDIFACTSegment] = None):
        self.message_type = message_type
        self.segments = segments or []
        self.interchange_control_ref = str(uuid.uuid4())[:14].upper()
        self.message_ref = str(uuid.uuid4())[:14].upper()
        self.datetime = datetime.datetime.now()

    def add_segment(self, segment: EDIFACTSegment) -> None:
        """Add a segment to the message."""
        self.segments.append(segment)

    def build_header(self) -> List[EDIFACTSegment]:
        """Build the standard EDIFACT header segments according to SARS specifications."""
        header_segments = []

        # UNA Service String Advice
        if Config.UNA_SEGMENT:
            header_segments.append(EDIFACTSegment("UNA", [
                EDIFACTElement(Config.UNA_SEGMENT[4:])
            ]))

        # UNB Interchange Header - SARS specific format
        date_format = self.datetime.strftime("%Y%m%d")
        time_format = self.datetime.strftime("%H%M")

        # Prepare recipient ID - add T suffix for test environment
        recipient_id = Config.RECIPIENT_ID
        if Config.RECIPIENT_TEST:
            recipient_id += "T"

        # Build sender identification
        sender_id = Config.SENDER_ID
        sp_comms_code = f"{Config.SENDER_SP_CODE}:{Config.SENDER_COMMS_CODE}" if Config.SENDER_SP_CODE else Config.SENDER_COMMS_CODE

        header_segments.append(EDIFACTSegment("UNB", [
            [EDIFACTElement(Config.SYNTAX_IDENTIFIER), EDIFACTElement(Config.SYNTAX_VERSION)],
            [EDIFACTElement(sender_id), EDIFACTElement(""), EDIFACTElement(Config.SENDER_AUTH_CODE), EDIFACTElement(sp_comms_code)],
            [EDIFACTElement(recipient_id), EDIFACTElement(""), EDIFACTElement(""), EDIFACTElement("")],
            [EDIFACTElement(date_format), EDIFACTElement(time_format)],
            EDIFACTElement(self.interchange_control_ref),
            EDIFACTElement(""),
            EDIFACTElement("CUSDEC"),
            EDIFACTElement(""),
            EDIFACTElement(""),
            EDIFACTElement(""),
            EDIFACTElement("1" if Config.RECIPIENT_TEST else "")
        ]))

        # UNH Message Header
        header_segments.append(EDIFACTSegment("UNH", [
            EDIFACTElement(self.message_ref),
            [EDIFACTElement(self.message_type), EDIFACTElement(Config.EDIFACT_VERSION),
             EDIFACTElement("2"), EDIFACTElement("")]
        ]))

        return header_segments

    def build_footer(self) -> List[EDIFACTSegment]:
        """Build the standard EDIFACT footer segments."""
        footer_segments = []

        # UNT Message Trailer
        segment_count = len(self.segments) + 2
        footer_segments.append(EDIFACTSegment("UNT", [
            EDIFACTElement(segment_count),
            EDIFACTElement(self.message_ref)
        ]))

        # UNZ Interchange Trailer
        footer_segments.append(EDIFACTSegment("UNZ", [
            EDIFACTElement("1"),
            EDIFACTElement(self.interchange_control_ref)
        ]))

        return footer_segments

    def format(self) -> str:
        """Format the complete EDIFACT message."""
        try:
            logger.info("Building header segments")
            header_segments = self.build_header()

            logger.info("Building footer segments")
            footer_segments = self.build_footer()

            logger.info("Combining all segments")
            all_segments = header_segments + self.segments + footer_segments

            logger.info("Formatting segments")
            result = ""
            for segment in all_segments:
                try:
                    result += segment.format()
                except Exception as e:
                    logger.error(f"Error formatting segment {segment.tag}: {str(e)}")
                    raise

            return result
        except Exception as e:
            logger.error(f"Error in format method: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise





