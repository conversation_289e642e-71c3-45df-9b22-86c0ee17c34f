"""
<PERSON><PERSON><PERSON> to generate a SARS CUSDEC message from a sample request.
"""

import os
import sys
import json
import logging

# Add parent directory to path to import CUSDEC module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from CUSDEC import CUSDEC<PERSON>uilder, CUSDECRequest

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Generate a SARS CUSDEC message from a sample request."""
    try:
        # Load sample request
        with open(os.path.join(os.path.dirname(__file__), 'sars_cusdec_request.json'), 'r') as f:
            request_data = json.load(f)
        
        # Validate request data
        request = CUSDECRequest(**request_data)
        logger.info(f"Loaded and validated request: {request.documentNumber}")
        
        # Build EDIFACT message
        builder = CUSDECBuilder(request.model_dump())
        edifact_message = builder.build()
        
        # Save EDIFACT message to file
        output_file = os.path.join(os.path.dirname(__file__), 'sars_cusdec_message.edi')
        with open(output_file, 'w') as f:
            f.write(edifact_message)
        
        logger.info(f"Generated EDIFACT message and saved to {output_file}")
        
        # Print message for verification
        print("\nGenerated EDIFACT message:")
        print("--------------------------")
        print(edifact_message)
        
    except Exception as e:
        logger.error(f"Error generating SARS CUSDEC message: {e}")
        raise

if __name__ == "__main__":
    main()
