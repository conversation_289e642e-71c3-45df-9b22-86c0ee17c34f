"""
Main application module for CUSDEC microservice.
"""

import os
import logging
from fastapi import FastAPI
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware

from config import Config
from database import create_tables
from api.routes import router

# Configure logging
log_file = os.getenv("LOG_FILE", "cusdec_service.log")
log_level = logging.DEBUG if Config.DEBUG_MODE else logging.INFO

logging.basicConfig(
    level=log_level,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(log_file)
    ]
)
logger = logging.getLogger("cusdec_service")

# Initialize database
create_tables()

# Initialize FastAPI app
app = FastAPI(
    title="CUSDEC Message Generator",
    description="Microservice for generating UN/EDIFACT CUSDEC messages",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv("ALLOWED_ORIGINS", "*").split(","),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(router)


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return JSONResponse(
        status_code=200,
        content={"status": "healthy", "service": "CUSDEC Message Generator"}
    )


if __name__ == "__main__":
    import uvicorn
    port = int(os.getenv("PORT", 8005))
    host = os.getenv("HOST", "0.0.0.0")
    
    logger.info(f"Starting CUSDEC service on {host}:{port}")
    uvicorn.run(app, host=host, port=port, reload=Config.DEBUG_MODE)


