"""
Pydantic models for CUSDEC API.
"""

import datetime
from typing import Dict, List, Optional
from pydantic import BaseModel, Field, validator


class Reference(BaseModel):
    """Model for a reference in the customs declaration."""
    qualifier: str = Field(..., description="Reference qualifier")
    number: str = Field(..., description="Reference number")


class Party(BaseModel):
    """Model for a party in the customs declaration."""
    partyIdQualifier: str = Field(..., description="Party identification qualifier")
    partyId: str = Field(..., description="Party identification")
    name: str = Field(..., description="Party name")
    street: Optional[str] = Field(None, description="Street address")
    city: Optional[str] = Field(None, description="City")
    postcode: Optional[str] = Field(None, description="Postal code")
    countryCode: str = Field(..., description="Country code")
    customsCode: Optional[str] = Field(None, description="SARS Customs Client Code")

    @validator('customsCode')
    def validate_customs_code(cls, v):
        """Validate SARS Customs Client Code format."""
        if v is not None:
            if len(v) < 5 or len(v) > 10:
                raise ValueError("SARS Customs Client Code must be between 5 and 10 characters")
        return v


class GoodsItem(BaseModel):
    """Model for a goods item in the customs declaration."""
    itemNumber: int = Field(..., description="Item number")
    commodityCode: str = Field(..., description="HS commodity code")
    goodsDescription: str = Field(..., description="Description of goods")
    quantity: float = Field(..., description="Quantity")
    quantityUnit: str = Field(..., description="Unit of quantity")
    itemValue: float = Field(..., description="Item value")
    customsValue: Optional[float] = Field(None, description="Customs value")
    dutyValue: Optional[float] = Field(None, description="Duty value in ZAR")
    vatValue: Optional[float] = Field(None, description="VAT value in ZAR")
    tariffCode: Optional[str] = Field(None, description="SARS Tariff Code")
    previousDocumentReference: Optional[str] = Field(None, description="Reference to previous document")
    preferentialTreatment: Optional[str] = Field(None, description="Preferential treatment code")

    @validator('commodityCode')
    def validate_commodity_code(cls, v):
        """Validate HS commodity code format."""
        if not v.isdigit() or len(v) < 6:
            raise ValueError("HS commodity code must be at least 6 digits")
        return v

    @validator('tariffCode')
    def validate_tariff_code(cls, v):
        """Validate SARS tariff code format."""
        if v is not None:
            if len(v) < 8:
                raise ValueError("SARS tariff code must be at least 8 characters")
        return v

    @validator('customsValue', 'dutyValue', 'vatValue')
    def validate_monetary_values(cls, v):
        """Validate monetary values."""
        if v is not None and v < 0:
            raise ValueError("Monetary values cannot be negative")
        return v


class MessageHeader(BaseModel):
    """D96B Message Header structure."""
    messageType: str = Field("CUSDEC", description="Message type")
    messageVersion: str = Field("D", description="Message version")
    messageRelease: str = Field("96B", description="Message release")
    controllingAgency: str = Field("UN", description="Controlling agency")
    messageReference: str = Field(..., description="Message reference")
    messageFunction: str = Field("9", description="Message function code")

class BeginningOfMessage(BaseModel):
    """BGM segment structure."""
    documentNameCode: str = Field("952", description="Document name code")
    documentNumber: str = Field(..., description="Document number")
    messageFunction: str = Field("9", description="Message function code")
    responseType: Optional[str] = Field(None, description="Response type")

class DateTimePeriod(BaseModel):
    """DTM segment structure."""
    qualifier: str = Field(..., description="Date/time qualifier")
    dateTime: str = Field(..., description="Date/time value")
    formatQualifier: str = Field("102", description="Format qualifier")

class LocationIdentification(BaseModel):
    """Location identification composite."""
    locationId: str = Field(..., description="Location ID")
    codeListQualifier: Optional[str] = Field(None, description="Code list qualifier")
    codeListResponsibleAgency: Optional[str] = Field(None, description="Code list agency")

class Location(BaseModel):
    """LOC segment structure."""
    qualifier: str = Field(..., description="Place/location qualifier")
    locationIdentification: LocationIdentification = Field(..., description="Location details")

class CUSDECRequest(BaseModel):
    """Enhanced CUSDEC request model aligned with D96B specification."""
    messageHeader: MessageHeader = Field(..., description="Message header")
    beginningOfMessage: BeginningOfMessage = Field(..., description="Beginning of message")
    dateTimePeriod: List[DateTimePeriod] = Field(..., description="Date/time periods")
    locations: List[Location] = Field(..., description="Locations")
    references: List[Reference] = Field([], description="References")
    nameAndAddress: List[Dict] = Field(..., description="Name and address parties")
    goodsItems: List[Dict] = Field(..., description="Goods items with D96B structure")
    controlTotal: Optional[List[Dict]] = Field(None, description="Control totals")
    authenticationResult: Optional[Dict] = Field(None, description="Authentication result")

    # Legacy fields for backward compatibility
    documentNumber: Optional[str] = Field(None, description="Legacy document number")
    documentNameCode: Optional[str] = Field(None, description="Legacy document name code")
    messageFunction: Optional[str] = Field(None, description="Legacy message function code")
    documentDate: Optional[str] = Field(None, description="Legacy document date")
    declarationDate: Optional[str] = Field(None, description="Legacy declaration date")
    placeOfDeparture: Optional[str] = Field(None, description="Legacy place of departure")
    placeOfDestination: Optional[str] = Field(None, description="Legacy place of destination")
    parties: Optional[Dict[str, Party]] = Field(None, description="Legacy parties structure")
    
    # SARS-specific fields
    declarationType: Optional[str] = Field(None, description="SARS Declaration Type")
    customsOfficeCode: Optional[str] = Field(None, description="SARS Customs Office Code")
    declarantReference: Optional[str] = Field(None, description="Declarant Reference")
    transportMode: Optional[str] = Field(None, description="Mode of Transport")
    exchangeRate: Optional[float] = Field(None, description="Exchange Rate")
    exchangeRateDate: Optional[str] = Field(None, description="Exchange Rate Date")
    totalInvoiceAmount: Optional[float] = Field(None, description="Total Invoice Amount")
    totalCustomsValue: Optional[float] = Field(None, description="Total Customs Value")
    totalDutyAmount: Optional[float] = Field(None, description="Total Duty Amount")
    totalVatAmount: Optional[float] = Field(None, description="Total VAT Amount")

    @validator('documentDate', 'declarationDate', 'exchangeRateDate')
    def validate_date_format(cls, v):
        """Validate date format (CCYYMMDD) as required by SARS."""
        if v is None:
            return v
        try:
            if len(v) == 6:
                datetime.datetime.strptime(v, '%y%m%d')
            elif len(v) == 8:
                datetime.datetime.strptime(v, '%Y%m%d')
            else:
                raise ValueError("Invalid date length")
            return v
        except (ValueError, IndexError):
            raise ValueError("Invalid date format")

    @validator('declarationType')
    def validate_declaration_type(cls, v):
        """Validate SARS Declaration Type Code."""
        if v is not None:
            valid_types = ['SAD500', 'SAD501', 'SAD502', 'SAD503', 'SAD504', 'SAD505', 'SAD506', 'SAD507', 'SAD508', 'SAD509', 'SAD510']
            if v not in valid_types:
                raise ValueError(f"Declaration type must be one of {valid_types}")
        return v

    @validator('customsOfficeCode')
    def validate_customs_office_code(cls, v):
        """Validate SARS Customs Office Code."""
        if v is not None:
            if len(v) != 3:
                raise ValueError("Customs office code must be 3 characters")
        return v

    @validator('parties')
    def validate_required_parties(cls, v):
        """Validate required parties according to SARS requirements."""
        required_parties = ['exporter', 'importer', 'declarant']
        for party in required_parties:
            if party not in v:
                raise ValueError(f"'{party}' is a required party according to SARS requirements")
        return v


class CUSDECResponse(BaseModel):
    """Model for the CUSDEC response."""
    message: str = Field(..., description="The generated EDIFACT message")
    messageReference: str = Field(..., description="Message reference number")


class CUSDECRecordCreate(CUSDECRequest):
    """Model for creating a CUSDEC record."""
    pass


class CUSDECRecordUpdate(BaseModel):
    """Model for updating a CUSDEC record."""
    documentNumber: Optional[str] = Field(None, description="Document number")
    documentNameCode: Optional[str] = Field(None, description="Document name code")
    messageFunction: Optional[str] = Field(None, description="Message function code")
    documentDate: Optional[str] = Field(None, description="Document date")
    declarationDate: Optional[str] = Field(None, description="Declaration date")
    placeOfDeparture: Optional[str] = Field(None, description="Place of departure code")
    placeOfDestination: Optional[str] = Field(None, description="Place of destination code")
    references: Optional[List[Reference]] = Field(None, description="References")
    parties: Optional[Dict[str, Party]] = Field(None, description="Parties involved")
    goodsItems: Optional[List[GoodsItem]] = Field(None, description="Goods items")
    controlResult: Optional[str] = Field(None, description="Control result")
    authenticationType: Optional[str] = Field(None, description="Authentication type")
    authenticationPlace: Optional[str] = Field(None, description="Authentication place")

    @validator('documentDate', 'declarationDate')
    def validate_date_format(cls, v):
        if v is None:
            return v
        try:
            datetime.datetime.strptime(v, '%y%m%d')
            return v
        except ValueError:
            raise ValueError('Date must be in YYMMDD format')


class CUSDECRecordResponse(BaseModel):
    """Model for the CUSDEC record response."""
    id: int = Field(..., description="Record ID")
    documentNumber: str = Field(..., description="Document number")
    documentNameCode: str = Field(..., description="Document name code")
    messageFunction: str = Field(..., description="Message function code")
    documentDate: str = Field(..., description="Document date")
    declarationDate: str = Field(..., description="Declaration date")
    placeOfDeparture: str = Field(..., description="Place of departure code")
    placeOfDestination: str = Field(..., description="Place of destination code")
    references: List[Reference] = Field([], description="References")
    parties: Dict[str, Party] = Field(..., description="Parties involved")
    goodsItems: List[GoodsItem] = Field(..., description="Goods items")
    controlResult: Optional[str] = Field(None, description="Control result")
    authenticationType: Optional[str] = Field(None, description="Authentication type")
    authenticationPlace: Optional[str] = Field(None, description="Authentication place")
    edifactMessage: str = Field(..., description="Generated EDIFACT message")
    messageReference: str = Field(..., description="Message reference number")
    createdAt: str = Field(..., description="Creation timestamp")
    updatedAt: str = Field(..., description="Last update timestamp")

