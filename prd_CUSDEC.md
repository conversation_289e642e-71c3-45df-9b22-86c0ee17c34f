# Product Requirements Document: CUSDEC Service

## Overview
This document outlines the requirements for the CUSDEC (Customs Declaration) microservice as part of the EDI Services Platform. The service will process UN/EDIFACT CUSDEC messages according to SARS (South African Revenue Service) guidelines and standards.

## Requirements

### Functional Requirements
1. The service shall process CUSDEC message types according to UN/EDIFACT standards and SARS specifications
2. The service shall monitor a specified directory for incoming CUSDEC files
3. The service shall parse and validate CUSDEC messages
4. The service shall transform CUSDEC data into structured format (JSON)
5. The service shall provide a REST API for message submission and querying
6. The service shall implement proper error handling for malformed messages
7. The service shall track processing status for each file
8. The service shall store processed messages in a database
9. The service shall support various customs declaration types (import, export, transit, etc.)

### Non-Functional Requirements
1. The service shall be containerized using Docker
2. The service shall follow the microservice architecture pattern established in the platform
3. The service shall include comprehensive logging
4. The service shall be configurable via environment variables
5. The service shall include health check endpoints
6. The service shall handle files in a stable and idempotent manner
7. The service shall include appropriate documentation
8. The service shall include a test suite

### Technical Requirements
1. The service shall be implemented in Python
2. The service shall use FastAPI for the REST API
3. The service shall use SQLite for development and PostgreSQL for production
4. The service shall include a Dockerfile and docker-compose.yml
5. The service shall follow the established project structure of other services
6. The service shall expose port 8001 for API access

## CUSDEC-Specific Requirements
1. The service shall handle CUSDEC message segments including BGM, RFF, DTM, LOC, MOA, TAX, NAD, GIS, PAC, PCI, and other segments specific to customs declarations
2. The service shall extract and process customs declaration information including:
   - Declaration type and function
   - Declarant and trader information
   - Goods descriptions and tariff classifications
   - Valuation information
   - Duty and tax calculations
   - Supporting document references
   - Container and packaging details
3. The service shall validate CUSDEC messages against SARS specifications
4. The service shall support the retrieval of declaration information by various identifiers (declaration number, MRN, etc.)
5. The service shall maintain a history of processed CUSDEC messages
6. The service shall support different message functions (original, amendment, cancellation)
7. The service shall handle both simplified and standard declarations

## API Endpoints
1. `GET /health`: Health check endpoint
2. `GET /api/v1/messages`: List all processed CUSDEC messages
3. `GET /api/v1/messages/{message_id}`: Get details of a specific CUSDEC message
4. `POST /api/v1/messages`: Submit a new CUSDEC message
5. `GET /api/v1/declarations`: List all customs declarations
6. `GET /api/v1/declarations/{declaration_id}`: Get details of a specific declaration
7. `GET /api/v1/files`: List all processed files
8. `GET /api/v1/files/{filename}`: Get the content of a specific processed file

## Deliverables
1. Source code for the CUSDEC service
2. Dockerfile and docker-compose.yml
3. Documentation including README.md
4. Test suite with sample CUSDEC messages
5. Configuration examples (.env.example)

## Constraints
1. The service must integrate with the existing EDI Services Platform
2. The service must follow the established patterns and conventions
3. The service must be compatible with the existing infrastructure
4. The service must comply with SARS CUSDEC message specifications
