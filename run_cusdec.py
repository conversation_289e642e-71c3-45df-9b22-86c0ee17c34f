"""
Launcher script for the CUSDEC microservice.
"""

import os
import uvicorn
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

if __name__ == "__main__":
    port = int(os.getenv("PORT", 8005))
    host = os.getenv("HOST", "0.0.0.0")
    debug_mode = os.getenv("DEBUG_MODE", "False").lower() == "true"

    print(f"Starting CUSDEC service on {host}:{port} (Debug mode: {debug_mode})")
    # Check if we're running in Docker or locally
    import os.path
    if os.path.exists("CUSDEC.py"):
        # We're in Docker
        uvicorn.run("CUSDEC:app", host=host, port=port, reload=debug_mode)
    else:
        # We're running locally
        uvicorn.run("CUSDEC.CUSDEC:app", host=host, port=port, reload=debug_mode)
