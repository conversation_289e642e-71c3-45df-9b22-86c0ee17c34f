import json
import pytest
from fastapi.testclient import TestClient

from CUSDEC import app, EDIFACTElement, EDIFACTSegment, EDIFACTMessage, CUSDECBuilder


client = TestClient(app)


def test_health_check():
    """Test the health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"


def test_edifact_element_formatting():
    """Test EDIFACT element formatting."""
    element = EDIFACTElement("TEST+VALUE")
    formatted = element.format()
    # Just check that the element is formatted
    assert formatted is not None
    assert len(formatted) > 0


def test_edifact_segment_formatting():
    """Test EDIFACT segment formatting."""
    segment = EDIFACTSegment("TST", [
        EDIFACTElement("ELEM1"),
        [EDIFACTElement("COMP1"), EDIFACTElement("COMP2")]
    ])
    formatted = segment.format()
    # Check that the segment is formatted
    assert formatted is not None
    assert len(formatted) > 0
    assert "TST" in formatted


def test_edifact_message_header():
    """Test EDIFACT message header generation."""
    message = EDIFACTMessage("CUSDEC")
    header_segments = message.build_header()

    # Check if UNB and UNH segments are present
    segment_tags = [segment.tag for segment in header_segments]
    assert "UNB" in segment_tags
    assert "UNH" in segment_tags


def test_generate_cusdec_valid_request():
    """Test CUSDEC generation with valid request data."""
    # Sample valid request data
    request_data = {
        "documentNameCode": "952",
        "documentNumber": "TESTDOC123",
        "messageFunction": "9",
        "documentDate": "230501",
        "declarationDate": "230501",
        "placeOfDeparture": "DEHAM",
        "placeOfDestination": "USLAX",
        "parties": {
            "exporter": {
                "partyIdQualifier": "ZZZ",
                "partyId": "EXPORTER123",
                "name": "Test Exporter",
                "street": "123 Export St",
                "city": "Hamburg",
                "postcode": "20457",
                "countryCode": "DE"
            },
            "importer": {
                "partyIdQualifier": "ZZZ",
                "partyId": "IMPORTER456",
                "name": "Test Importer",
                "street": "456 Import Ave",
                "city": "Los Angeles",
                "postcode": "90001",
                "countryCode": "US"
            }
        },
        "goodsItems": [
            {
                "description": "Test Product",
                "packageType": "BX",
                "packageCount": 10,
                "grossWeight": 100.5,
                "netWeight": 95.0,
                "commodityCode": "8471.30.0000",
                "originCountry": "DE",
                "itemValue": 5000.00
            }
        ]
    }

    response = client.post("/api/cusdec", json=request_data)
    assert response.status_code == 200

    # Check if response contains the message and reference
    response_data = response.json()
    assert "message" in response_data
    assert "messageReference" in response_data

    # Check if the message is not empty
    message = response_data["message"]
    assert message is not None
    assert len(message) > 0


def test_generate_cusdec_invalid_request():
    """Test CUSDEC generation with invalid request data."""
        # Sample invalid request missing required fields
    request_data = {
        "documentNumber": "TESTDOC123",
        "documentDate": "230501",
        "declarationDate": "230501",
        # Missing placeOfDeparture and placeOfDestination
        "parties": {
            # Missing importer
            "exporter": {
                "partyIdQualifier": "ZZZ",
                "partyId": "EXPORTER123",
                "name": "Test Exporter",
                "street": "123 Export St",
                "city": "Hamburg",
                "postcode": "20457",
                "countryCode": "DE"
            }
        },
        "goodsItems": []  # Empty goods items
    }

    response = client.post("/api/cusdec", json=request_data)
    assert response.status_code == 422  # Unprocessable Entity


def test_generate_cusdec_invalid_date_format():
    """Test CUSDEC generation with invalid date format."""
    request_data = {
        "documentNameCode": "952",
        "documentNumber": "TESTDOC123",
        "messageFunction": "9",
        "documentDate": "2023-05-01",  # Wrong format, should be YYMMDD
        "declarationDate": "230501",
        "placeOfDeparture": "DEHAM",
        "placeOfDestination": "USLAX",
        "parties": {
            "exporter": {
                "partyIdQualifier": "ZZZ",
                "partyId": "EXPORTER123",
                "name": "Test Exporter",
                "street": "123 Export St",
                "city": "Hamburg",
                "postcode": "20457",
                "countryCode": "DE"
            },
            "importer": {
                "partyIdQualifier": "ZZZ",
                "partyId": "IMPORTER456",
                "name": "Test Importer",
                "street": "456 Import Ave",
                "city": "Los Angeles",
                "postcode": "90001",
                "countryCode": "US"
            }
        },
        "goodsItems": [
            {
                "description": "Test Product",
                "packageType": "BX",
                "packageCount": 10,
                "grossWeight": 100.5,
                "netWeight": 95.0,
                "commodityCode": "8471.30.0000",
                "originCountry": "DE",
                "itemValue": 5000.00
            }
        ]
    }

    response = client.post("/api/cusdec", json=request_data)
    assert response.status_code == 422
    assert "Date must be in YYMMDD format" in str(response.json())
