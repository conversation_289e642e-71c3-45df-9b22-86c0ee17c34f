#!/usr/bin/env python3
"""
Integration test for SARS CUSDEC functionality.
Tests the complete flow from JSON input to EDIFACT output according to SARS CUSDEC_30_2 specification.
"""

import json
import requests
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_sample_data():
    """Load the SARS CUSDEC sample data."""
    sample_file = Path("data/sars_cusdec_sample.json")
    with open(sample_file, 'r') as f:
        return json.load(f)

def test_sars_validation():
    """Test SARS CUSDEC validation endpoint."""
    logger.info("Testing SARS CUSDEC validation...")

    sample_data = load_sample_data()

    try:
        response = requests.post(
            "http://localhost:8005/api/v1/cusdec/validate-sars",
            json=sample_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            logger.info(f"Validation result: {result}")
            return result["valid"]
        else:
            logger.error(f"Validation failed with status {response.status_code}: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        logger.error("Could not connect to CUSDEC service. Is it running on port 8005?")
        return False
    except Exception as e:
        logger.error(f"Error during validation test: {e}")
        return False

def test_sars_generation():
    """Test SARS CUSDEC message generation."""
    logger.info("Testing SARS CUSDEC generation...")

    sample_data = load_sample_data()

    try:
        response = requests.post(
            "http://localhost:8005/api/v1/cusdec",
            json=sample_data,
            headers={"Content-Type": "application/json"}
        )

        if response.status_code == 201:
            result = response.json()
            logger.info("SARS CUSDEC generation successful!")
            logger.info(f"Generated message preview: {result['edifact_message'][:200]}...")
            return True
        else:
            logger.error(f"Generation failed with status {response.status_code}: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        logger.error("Could not connect to CUSDEC service. Is it running on port 8005?")
        return False
    except Exception as e:
        logger.error(f"Error during generation test: {e}")
        return False

def test_legacy_compatibility():
    """Test that legacy format still works."""
    logger.info("Testing legacy format compatibility...")
    
    # Load legacy sample
    legacy_file = Path("sample_request.json")
    with open(legacy_file, 'r') as f:
        legacy_data = json.load(f)
    
    try:
        response = requests.post(
            "http://localhost:8005/api/v1/cusdec",
            json=legacy_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 201:
            result = response.json()
            logger.info("Legacy format generation successful!")
            return True
        else:
            logger.error(f"Legacy generation failed with status {response.status_code}: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        logger.error("Could not connect to CUSDEC service. Is it running on port 8005?")
        return False
    except Exception as e:
        logger.error(f"Error during legacy test: {e}")
        return False

def compare_outputs():
    """Compare SARS and legacy outputs to ensure they're different but valid."""
    logger.info("Comparing SARS vs Legacy outputs...")

    # This would require both formats to generate successfully
    # and then compare the EDIFACT structure
    logger.info("Output comparison would be implemented here")
    return True

def main():
    """Run all SARS CUSDEC integration tests."""
    logger.info("Starting SARS CUSDEC Integration Tests")
    logger.info("=" * 50)

    tests = [
        ("SARS Validation", test_sars_validation),
        ("SARS Generation", test_sars_generation),
        ("Legacy Compatibility", test_legacy_compatibility),
        ("Output Comparison", compare_outputs)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} ---")
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"Test {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("TEST RESULTS SUMMARY")
    logger.info("=" * 50)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "PASS" if result else "FAIL"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All SARS CUSDEC integration tests PASSED!")
        return True
    else:
        logger.warning(f"⚠️  {total - passed} test(s) FAILED")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
