"""
Tests for SARS-specific implementation of CUSDEC service.
"""

import pytest
import json
from CUSDEC import CUSDECBuilder, CUSDECRequest, Party, GoodsItem, Reference
from pydantic import ValidationError

def test_sars_tin_number_validation():
    """Test validation of SARS TIN number."""
    # Valid TIN number
    party = Party(
        partyId="EXPORTER123",
        name="Test Exporter",
        street="123 Export St",
        city="Johannesburg",
        postcode="2000",
        countryCode="ZA",
        tinNumber="1234567890"
    )
    assert party.tinNumber == "1234567890"
    
    # Invalid TIN number (too short)
    with pytest.raises(ValidationError):
        Party(
            partyId="EXPORTER123",
            name="Test Exporter",
            street="123 Export St",
            city="Johannesburg",
            postcode="2000",
            countryCode="ZA",
            tinNumber="12345"
        )
    
    # Invalid TIN number (non-numeric)
    with pytest.raises(ValidationError):
        Party(
            partyId="EXPORTER123",
            name="Test Exporter",
            street="123 Export St",
            city="Johannesburg",
            postcode="2000",
            countryCode="ZA",
            tinNumber="123ABC7890"
        )

def test_sars_vat_number_validation():
    """Test validation of SARS VAT number."""
    # Valid VAT number
    request = CUSDECRequest(
        documentNumber="DOC123456",
        documentDate="230101",
        declarationDate="230101",
        placeOfDeparture="ZADUR",
        placeOfDestination="ZAJNB",
        parties={
            "exporter": Party(
                partyId="EXPORTER123",
                name="Test Exporter",
                street="123 Export St",
                city="Johannesburg",
                postcode="2000",
                countryCode="ZA"
            ),
            "importer": Party(
                partyId="IMPORTER456",
                name="Test Importer",
                street="456 Import St",
                city="Cape Town",
                postcode="8000",
                countryCode="ZA"
            )
        },
        goodsItems=[
            GoodsItem(
                description="Test Goods",
                packageType="BX",
                packageCount=10,
                grossWeight=100.0,
                netWeight=90.0,
                commodityCode="847130",
                originCountry="ZA",
                itemValue=1000.0
            )
        ],
        valueAddedTaxNumber="4123456789"
    )
    assert request.valueAddedTaxNumber == "4123456789"
    
    # Invalid VAT number (doesn't start with 4)
    with pytest.raises(ValidationError):
        CUSDECRequest(
            documentNumber="DOC123456",
            documentDate="230101",
            declarationDate="230101",
            placeOfDeparture="ZADUR",
            placeOfDestination="ZAJNB",
            parties={
                "exporter": Party(
                    partyId="EXPORTER123",
                    name="Test Exporter",
                    street="123 Export St",
                    city="Johannesburg",
                    postcode="2000",
                    countryCode="ZA"
                ),
                "importer": Party(
                    partyId="IMPORTER456",
                    name="Test Importer",
                    street="456 Import St",
                    city="Cape Town",
                    postcode="8000",
                    countryCode="ZA"
                )
            },
            goodsItems=[
                GoodsItem(
                    description="Test Goods",
                    packageType="BX",
                    packageCount=10,
                    grossWeight=100.0,
                    netWeight=90.0,
                    commodityCode="847130",
                    originCountry="ZA",
                    itemValue=1000.0
                )
            ],
            valueAddedTaxNumber="1234567890"
        )

def test_sars_declaration_type_validation():
    """Test validation of SARS declaration type."""
    # Valid declaration type
    request = CUSDECRequest(
        documentNumber="DOC123456",
        documentDate="230101",
        declarationDate="230101",
        placeOfDeparture="ZADUR",
        placeOfDestination="ZAJNB",
        parties={
            "exporter": Party(
                partyId="EXPORTER123",
                name="Test Exporter",
                street="123 Export St",
                city="Johannesburg",
                postcode="2000",
                countryCode="ZA"
            ),
            "importer": Party(
                partyId="IMPORTER456",
                name="Test Importer",
                street="456 Import St",
                city="Cape Town",
                postcode="8000",
                countryCode="ZA"
            )
        },
        goodsItems=[
            GoodsItem(
                description="Test Goods",
                packageType="BX",
                packageCount=10,
                grossWeight=100.0,
                netWeight=90.0,
                commodityCode="847130",
                originCountry="ZA",
                itemValue=1000.0
            )
        ],
        declarationType="SAD500"
    )
    assert request.declarationType == "SAD500"
    
    # Invalid declaration type
    with pytest.raises(ValidationError):
        CUSDECRequest(
            documentNumber="DOC123456",
            documentDate="230101",
            declarationDate="230101",
            placeOfDeparture="ZADUR",
            placeOfDestination="ZAJNB",
            parties={
                "exporter": Party(
                    partyId="EXPORTER123",
                    name="Test Exporter",
                    street="123 Export St",
                    city="Johannesburg",
                    postcode="2000",
                    countryCode="ZA"
                ),
                "importer": Party(
                    partyId="IMPORTER456",
                    name="Test Importer",
                    street="456 Import St",
                    city="Cape Town",
                    postcode="8000",
                    countryCode="ZA"
                )
            },
            goodsItems=[
                GoodsItem(
                    description="Test Goods",
                    packageType="BX",
                    packageCount=10,
                    grossWeight=100.0,
                    netWeight=90.0,
                    commodityCode="847130",
                    originCountry="ZA",
                    itemValue=1000.0
                )
            ],
            declarationType="INVALID"
        )

def test_sars_specific_segments():
    """Test generation of SARS-specific segments in EDIFACT message."""
    # Create a request with SARS-specific fields
    request = CUSDECRequest(
        documentNumber="DOC123456",
        documentDate="230101",
        declarationDate="230101",
        placeOfDeparture="ZADUR",
        placeOfDestination="ZAJNB",
        parties={
            "exporter": Party(
                partyId="EXPORTER123",
                name="Test Exporter",
                street="123 Export St",
                city="Johannesburg",
                postcode="2000",
                countryCode="ZA",
                tinNumber="1234567890"
            ),
            "importer": Party(
                partyId="IMPORTER456",
                name="Test Importer",
                street="456 Import St",
                city="Cape Town",
                postcode="8000",
                countryCode="ZA"
            )
        },
        goodsItems=[
            GoodsItem(
                description="Test Goods",
                packageType="BX",
                packageCount=10,
                grossWeight=100.0,
                netWeight=90.0,
                commodityCode="847130",
                originCountry="ZA",
                itemValue=1000.0,
                customsValue=1100.0,
                dutyValue=110.0,
                vatValue=154.0,
                tariffCode="84713000",
                preferentialTreatment="A"
            )
        ],
        declarationType="SAD500",
        customsOfficeCode="JHB",
        transportMode="4",
        valueAddedTaxNumber="4123456789",
        declarantReference="REF123456"
    )
    
    # Build EDIFACT message
    builder = CUSDECBuilder(request.model_dump())
    edifact_message = builder.build()
    
    # Check for SARS-specific segments
    assert "DOC+ZZZ+SAD500:271" in edifact_message  # Declaration type
    assert "CTA+CR+:JHB" in edifact_message  # Customs office
    assert "TDT+1+++" in edifact_message  # Transport mode
    assert "RFF+VA:4123456789" in edifact_message  # VAT number
    assert "RFF+ABO:REF123456" in edifact_message  # Declarant reference
    assert "LOC+42+JHB:162" in edifact_message  # Customs office location
    assert "DTM+132:230101:102" in edifact_message  # Clearance date
    
    # Check for SARS-specific goods item segments
    assert "MOA+146:1100.0" in edifact_message  # Customs value
    assert "TAX+7+A+A00++" in edifact_message  # Duty
    assert "TAX+7+A+VAT++" in edifact_message  # VAT
    assert "PIA+5+84713000:HS" in edifact_message  # Tariff code
    assert "ALC+A+++1:A" in edifact_message  # Preferential treatment
