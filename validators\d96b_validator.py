"""
SARS CUSDEC Message Validator
Validates JSON input against SARS CUSDEC_30_2 specification.
Based on CUSDEC_30_2.pdf requirements for South African Revenue Service.
"""

import json
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

logger = logging.getLogger("cusdec_service")


class SARSCUSDECValidator:
    """Validator for SARS CUSDEC_30_2 message structure."""

    def __init__(self, schema_path: Optional[str] = None):
        """Initialize validator with SARS CUSDEC schema."""
        if schema_path is None:
            schema_path = Path(__file__).parent.parent / "data" / "cusdec_d96b.json"

        self.schema_path = schema_path
        self.schema = self._load_schema()
        
    def _load_schema(self) -> Dict[str, Any]:
        """Load the SARS CUSDEC schema definition."""
        try:
            with open(self.schema_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load SARS CUSDEC schema: {e}")
            return {}
    
    def validate_message_header(self, data: Dict[str, Any]) -> List[str]:
        """Validate message header structure."""
        errors = []
        
        if "messageHeader" not in data:
            errors.append("Missing required messageHeader")
            return errors
            
        header = data["messageHeader"]
        
        # Validate required fields
        required_fields = ["messageType", "messageVersion", "messageRelease", 
                          "controllingAgency", "messageReference", "messageFunction"]
        
        for field in required_fields:
            if field not in header:
                errors.append(f"Missing required field in messageHeader: {field}")
        
        # Validate values for SARS CUSDEC
        if header.get("messageType") != "CUSDEC":
            errors.append("messageType must be 'CUSDEC'")

        # SARS uses version 30, not D96B
        if header.get("messageVersion") not in ["30", "D"]:
            errors.append("messageVersion must be '30' (SARS) or 'D' (generic)")

        if header.get("messageRelease") and header.get("messageRelease") not in ["96B", "30"]:
            errors.append("messageRelease must be '96B' or '30' for SARS")

        if header.get("controllingAgency") not in ["UN", "UNO", "SARS"]:
            errors.append("controllingAgency must be 'UN', 'UNO', or 'SARS'")
            
        return errors
    
    def validate_bgm_segment(self, data: Dict[str, Any]) -> List[str]:
        """Validate Beginning of Message (BGM) segment."""
        errors = []
        
        if "beginningOfMessage" not in data:
            errors.append("Missing required beginningOfMessage (BGM segment)")
            return errors
            
        bgm = data["beginningOfMessage"]
        
        # Validate document name code
        if "documentNameCode" in bgm:
            valid_codes = ["952", "955", "956"]  # Common CUSDEC document codes
            if bgm["documentNameCode"] not in valid_codes:
                errors.append(f"Invalid documentNameCode: {bgm['documentNameCode']}")
        
        # Validate message function
        if "messageFunction" in bgm:
            valid_functions = ["1", "2", "3", "4", "5", "6", "7", "8", "9"]
            if bgm["messageFunction"] not in valid_functions:
                errors.append(f"Invalid messageFunction: {bgm['messageFunction']}")
                
        return errors
    
    def validate_dtm_segments(self, data: Dict[str, Any]) -> List[str]:
        """Validate Date/Time (DTM) segments."""
        errors = []
        
        if "dateTimePeriod" not in data:
            errors.append("Missing required dateTimePeriod (DTM segments)")
            return errors
            
        dtm_segments = data["dateTimePeriod"]
        
        if not isinstance(dtm_segments, list):
            errors.append("dateTimePeriod must be a list")
            return errors
            
        for i, dtm in enumerate(dtm_segments):
            if "qualifier" not in dtm:
                errors.append(f"DTM segment {i}: missing qualifier")
            if "dateTime" not in dtm:
                errors.append(f"DTM segment {i}: missing dateTime")
                
            # Validate date format
            if "dateTime" in dtm and "formatQualifier" in dtm:
                if dtm["formatQualifier"] == "102":  # CCYYMMDD format
                    if len(dtm["dateTime"]) != 8:
                        errors.append(f"DTM segment {i}: invalid date format for qualifier 102")
                        
        return errors
    
    def validate_loc_segments(self, data: Dict[str, Any]) -> List[str]:
        """Validate Location (LOC) segments."""
        errors = []
        
        if "locations" not in data:
            errors.append("Missing required locations (LOC segments)")
            return errors
            
        locations = data["locations"]
        
        if not isinstance(locations, list):
            errors.append("locations must be a list")
            return errors
            
        for i, loc in enumerate(locations):
            if "qualifier" not in loc:
                errors.append(f"LOC segment {i}: missing qualifier")
            if "locationIdentification" not in loc:
                errors.append(f"LOC segment {i}: missing locationIdentification")
            else:
                loc_id = loc["locationIdentification"]
                if "locationId" not in loc_id:
                    errors.append(f"LOC segment {i}: missing locationId")
                    
        return errors
    
    def validate_nad_segments(self, data: Dict[str, Any]) -> List[str]:
        """Validate Name and Address (NAD) segments."""
        errors = []
        
        if "nameAndAddress" not in data:
            errors.append("Missing required nameAndAddress (NAD segments)")
            return errors
            
        nad_segments = data["nameAndAddress"]
        
        if not isinstance(nad_segments, list):
            errors.append("nameAndAddress must be a list")
            return errors
            
        # Check for required party qualifiers
        qualifiers = [nad.get("partyQualifier") for nad in nad_segments]
        required_qualifiers = ["EX", "IM"]  # Exporter and Importer are mandatory
        
        for req_qual in required_qualifiers:
            if req_qual not in qualifiers:
                errors.append(f"Missing required party qualifier: {req_qual}")
                
        return errors
    
    def validate_goods_items(self, data: Dict[str, Any]) -> List[str]:
        """Validate goods items structure."""
        errors = []
        
        if "goodsItems" not in data:
            errors.append("Missing required goodsItems")
            return errors
            
        goods_items = data["goodsItems"]
        
        if not isinstance(goods_items, list):
            errors.append("goodsItems must be a list")
            return errors
            
        if len(goods_items) == 0:
            errors.append("At least one goods item is required")
            
        for i, item in enumerate(goods_items):
            if "lineItem" not in item:
                errors.append(f"Goods item {i}: missing lineItem")
            if "itemDescription" not in item:
                errors.append(f"Goods item {i}: missing itemDescription")
                
        return errors

    def validate_sars_specific_fields(self, data: Dict[str, Any]) -> List[str]:
        """Validate SARS-specific fields and requirements."""
        errors = []

        # Check for SARS-specific fields
        sars_fields = {
            "declarationType": "Declaration Type (required for SARS)",
            "customsOfficeCode": "Customs Office Code (required for SARS)",
            "declarantReference": "Declarant Reference",
            "transportMode": "Transport Mode"
        }

        for field, description in sars_fields.items():
            if field in data:
                value = data[field]
                if field == "declarationType" and value not in ["SAD500", "SAD501", "SAD502", "SAD503", "SAD504"]:
                    errors.append(f"Invalid declarationType: {value}. Must be one of SAD500, SAD501, SAD502, SAD503, SAD504")
                elif field == "customsOfficeCode" and (not value or len(value) != 3):
                    errors.append(f"Invalid customsOfficeCode: {value}. Must be 3 characters")
                elif field == "transportMode" and value not in ["1", "2", "3", "4", "5", "6", "7", "8", "9"]:
                    errors.append(f"Invalid transportMode: {value}. Must be 1-9")

        # Validate SARS parties requirements
        parties = data.get("parties", {})
        required_sars_parties = ["exporter", "importer", "declarant"]
        for party in required_sars_parties:
            if party not in parties:
                errors.append(f"Missing required SARS party: {party}")

        # Validate TIN numbers for SARS parties
        for party_name, party_data in parties.items():
            if isinstance(party_data, dict):
                tin_number = party_data.get("tinNumber")
                if tin_number and (not tin_number.isdigit() or len(tin_number) != 10):
                    errors.append(f"Invalid TIN number for {party_name}: {tin_number}. Must be 10 digits")

                vat_number = party_data.get("vatNumber")
                if vat_number and (not vat_number.isdigit() or len(vat_number) != 10 or not vat_number.startswith("4")):
                    errors.append(f"Invalid VAT number for {party_name}: {vat_number}. Must be 10 digits starting with 4")

        return errors

    def validate_full_message(self, data: Dict[str, Any]) -> List[str]:
        """Perform full SARS CUSDEC message validation."""
        errors = []

        # Check if this is D96B format or legacy SARS format
        if self._is_d96b_format(data):
            # Validate D96B structure
            errors.extend(self.validate_message_header(data))
            errors.extend(self.validate_bgm_segment(data))
            errors.extend(self.validate_dtm_segments(data))
            errors.extend(self.validate_loc_segments(data))
            errors.extend(self.validate_nad_segments(data))
            errors.extend(self.validate_goods_items(data))
        else:
            # Validate legacy SARS format
            errors.extend(self.validate_legacy_sars_format(data))

        # Always validate SARS-specific requirements
        errors.extend(self.validate_sars_specific_fields(data))

        return errors

    def _is_d96b_format(self, data: Dict[str, Any]) -> bool:
        """Check if data is in D96B format."""
        return "messageHeader" in data and "beginningOfMessage" in data

    def validate_legacy_sars_format(self, data: Dict[str, Any]) -> List[str]:
        """Validate legacy SARS CUSDEC format."""
        errors = []

        # Check required fields for legacy format
        required_fields = ["documentNumber", "documentDate", "declarationDate", "parties", "goodsItems"]
        for field in required_fields:
            if field not in data:
                errors.append(f"Missing required field: {field}")

        # Validate date formats
        for date_field in ["documentDate", "declarationDate"]:
            if date_field in data:
                date_value = data[date_field]
                if not isinstance(date_value, str) or len(date_value) != 6:
                    errors.append(f"Invalid {date_field} format: {date_value}. Must be YYMMDD")

        return errors
    
    def is_valid(self, data: Dict[str, Any]) -> bool:
        """Check if message is valid according to SARS CUSDEC specification."""
        errors = self.validate_full_message(data)
        return len(errors) == 0

    def get_validation_report(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Get detailed validation report."""
        errors = self.validate_full_message(data)
        format_type = "D96B" if self._is_d96b_format(data) else "Legacy SARS"

        return {
            "valid": len(errors) == 0,
            "error_count": len(errors),
            "errors": errors,
            "format": format_type,
            "specification": "SARS CUSDEC_30_2",
            "message": f"Valid {format_type} SARS CUSDEC message" if len(errors) == 0 else f"Invalid {format_type} SARS CUSDEC message"
        }


# Backward compatibility alias
D96BValidator = SARSCUSDECValidator
